#!/usr/bin/env python3
"""
缺陷自动打标快速启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("="*60)
    print("缺陷自动打标工具")
    print("="*60)
    
    # 检查Python环境
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return
    
    # 检查项目根目录
    project_root = Path(__file__).parent
    script_path = project_root / "backend/app/scripts/bug_auto_labeling.py"
    
    if not script_path.exists():
        print(f"❌ 找不到主程序文件: {script_path}")
        return
    
    print("🚀 启动缺陷自动打标程序...")
    print()
    
    # 构建命令
    cmd = [sys.executable, str(script_path)]
    
    # 添加命令行参数
    if len(sys.argv) > 1:
        cmd.extend(sys.argv[1:])
    else:
        # 默认参数：获取最近3天的数据，限制50个
        cmd.extend(["--days", "3", "--limit", "50"])
        print("使用默认参数: --days 3 --limit 50")
        print("如需自定义参数，请使用: python run_bug_labeling.py --help")
        print()
    
    try:
        # 执行主程序
        result = subprocess.run(cmd, cwd=project_root)
        
        if result.returncode == 0:
            print("\n✅ 程序执行完成")
        else:
            print(f"\n❌ 程序执行失败，退出码: {result.returncode}")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {str(e)}")

if __name__ == "__main__":
    main()
