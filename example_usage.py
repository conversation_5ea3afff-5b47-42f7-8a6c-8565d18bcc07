#!/usr/bin/env python3
"""
缺陷自动打标功能使用示例
演示如何在代码中使用各个模块
"""
import os
import sys
import asyncio
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '.')))

from backend.app.service.bug_labeling import (
    label_manager,
    labeling_service,
    bug_data_service,
    excel_export_service
)


async def example_1_basic_labeling():
    """示例1: 基本的BUG打标"""
    print("="*50)
    print("示例1: 基本的BUG打标")
    print("="*50)
    
    # 创建测试BUG数据
    bug_data = {
        "ID": "example_001",
        "标题": "智能导诊页面年龄选择器无法点击",
        "详细描述": """
        【条件】测试环境，iOS 15.0系统
        【步骤】
        1. 打开健康问问APP
        2. 进入智能导诊页面
        3. 尝试点击年龄选择器
        【预期结果】能够正常选择年龄
        【实际结果】点击年龄选择器无任何反应，无法进行下一步
        """,
        "模块": "健康问问",
        "状态": "新建",
        "创建人": "测试用户"
    }
    
    print("BUG信息:")
    print(f"  标题: {bug_data['标题']}")
    print(f"  模块: {bug_data['模块']}")
    
    # 执行打标
    print("\n正在调用大模型进行打标...")
    result = await labeling_service.label_bug(bug_data)
    
    if result.error_message:
        print(f"❌ 打标失败: {result.error_message}")
    else:
        print("✅ 打标成功!")
        print(f"  问题类型: {result.problem_type}")
        print(f"  具体功能: {result.specific_function}")
        print(f"  置信度: {result.confidence:.2f}")
        print(f"  推理过程: {result.reasoning[:100]}...")
    
    return result


async def example_2_batch_processing():
    """示例2: 批量处理BUG"""
    print("\n" + "="*50)
    print("示例2: 批量获取和处理BUG")
    print("="*50)
    
    # 获取最近的BUG数据
    print("正在获取最近的BUG数据...")
    bugs = bug_data_service.get_recent_bugs(days=1, limit=3)
    
    print(f"获取到 {len(bugs)} 个BUG")
    
    if not bugs:
        print("⚠️  没有获取到BUG数据，可能是时间范围内没有符合条件的BUG")
        return []
    
    # 批量打标
    results = []
    for i, bug in enumerate(bugs, 1):
        print(f"\n处理BUG [{i}/{len(bugs)}]: {bug.get('标题', '未知标题')[:50]}...")
        
        try:
            result = await labeling_service.label_bug(bug)
            results.append({
                'bug_data': bug,
                'labeling_result': result
            })
            
            if result.error_message:
                print(f"  ❌ 失败: {result.error_message}")
            else:
                print(f"  ✅ 成功: {result.problem_type} | {result.specific_function}")
                
        except Exception as e:
            print(f"  ❌ 异常: {str(e)}")
    
    return results


def example_3_label_management():
    """示例3: 标签管理"""
    print("\n" + "="*50)
    print("示例3: 标签管理")
    print("="*50)
    
    # 查看当前标签
    print("当前标签库:")
    labels = label_manager.get_all_labels()
    print(f"  问题类型 ({len(labels.get('问题类型', []))} 个):")
    for ptype in labels.get('问题类型', [])[:3]:
        print(f"    - {ptype}")
    if len(labels.get('问题类型', [])) > 3:
        print(f"    ... 还有 {len(labels.get('问题类型', [])) - 3} 个")
    
    print(f"  具体功能 ({len(labels.get('具体功能', []))} 个):")
    for func in labels.get('具体功能', [])[:3]:
        print(f"    - {func}")
    if len(labels.get('具体功能', [])) > 3:
        print(f"    ... 还有 {len(labels.get('具体功能', [])) - 3} 个")
    
    # 添加新标签（示例）
    print("\n添加示例标签:")
    test_problem_type = f"示例问题类型_{hash('test') % 1000}"
    test_function = f"示例功能_{hash('test') % 1000}"
    
    success1 = label_manager.add_problem_type(test_problem_type)
    success2 = label_manager.add_specific_function(test_function)
    
    if success1:
        print(f"  ✅ 添加问题类型: {test_problem_type}")
    if success2:
        print(f"  ✅ 添加具体功能: {test_function}")
    
    # 显示统计
    stats = label_manager.get_labels_summary()
    print(f"\n标签统计: {stats}")


def example_4_excel_export(results):
    """示例4: Excel导出"""
    print("\n" + "="*50)
    print("示例4: Excel导出")
    print("="*50)
    
    if not results:
        print("⚠️  没有打标结果可导出")
        return
    
    try:
        # 导出详细结果
        excel_file = excel_export_service.export_labeling_results(
            results,
            filename="example_labeling_results.xlsx"
        )
        print(f"✅ 详细结果已导出: {excel_file}")
        
        # 导出汇总报告
        summary_file = excel_export_service.export_summary_report(
            results,
            filename="example_summary_report.xlsx"
        )
        print(f"✅ 汇总报告已导出: {summary_file}")
        
        print(f"\n导出文件位置: {Path(excel_file).parent}")
        
    except Exception as e:
        print(f"❌ 导出失败: {str(e)}")


def example_5_statistics():
    """示例5: 统计信息"""
    print("\n" + "="*50)
    print("示例5: 获取统计信息")
    print("="*50)
    
    try:
        # 获取BUG统计
        stats = bug_data_service.get_statistics()
        
        if stats:
            print("BUG数据统计:")
            print(f"  总数量: {stats.get('总数量', 0)}")
            print(f"  时间范围: {stats.get('时间范围', 'N/A')}")
            
            status_dist = stats.get('状态分布', {})
            if status_dist:
                print("  状态分布:")
                for status, count in status_dist.items():
                    print(f"    {status}: {count}")
        else:
            print("⚠️  无法获取统计信息")
            
    except Exception as e:
        print(f"❌ 获取统计信息失败: {str(e)}")


async def main():
    """主函数"""
    print("缺陷自动打标功能使用示例")
    print("本示例将演示各个模块的基本用法")
    print()
    
    try:
        # 示例1: 基本打标
        result1 = await example_1_basic_labeling()
        
        # 示例2: 批量处理
        results = await example_2_batch_processing()
        
        # 示例3: 标签管理
        example_3_label_management()
        
        # 示例4: Excel导出
        if results:
            example_4_excel_export(results)
        
        # 示例5: 统计信息
        example_5_statistics()
        
        print("\n" + "="*50)
        print("示例演示完成!")
        print("="*50)
        print("更多用法请参考:")
        print("1. README_BUG_LABELING.md - 详细文档")
        print("2. backend/app/scripts/bug_auto_labeling.py - 完整程序")
        print("3. backend/app/scripts/test_bug_labeling.py - 功能测试")
        
    except Exception as e:
        print(f"\n❌ 示例执行失败: {str(e)}")
        print("请检查配置和环境是否正确")


if __name__ == "__main__":
    asyncio.run(main())
