#!/usr/bin/env python3
"""
测试简化后的缺陷自动打标功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath('.'))

from backend.app.service.bug_labeling.bug_data_service import BugDataService

def test_simplified_bug_service():
    """测试简化后的BUG数据服务"""
    print("="*50)
    print("测试简化后的BUG数据服务")
    print("="*50)
    
    service = BugDataService()
    
    # 测试获取BUG数据（不再需要时间参数）
    print("1. 测试获取BUG数据...")
    bugs = service.get_recent_bugs(limit=5)
    print(f"   获取到 {len(bugs)} 个BUG")
    
    if bugs:
        print("   第一个BUG信息:")
        first_bug = bugs[0]
        print(f"     ID: {first_bug.get('ID', 'N/A')}")
        print(f"     标题: {first_bug.get('标题', 'N/A')[:50]}...")
        print(f"     模块: {first_bug.get('模块', 'N/A')}")
        print(f"     状态: {first_bug.get('状态', 'N/A')}")
    
    # 测试统计信息（不再需要时间参数）
    print("\n2. 测试统计信息...")
    stats = service.get_statistics()
    print(f"   统计结果: {stats}")
    
    print("\n✅ 简化后的服务测试完成！")
    print("主要改进:")
    print("- 只通过API参数进行module和status过滤")
    print("- 删除了所有客户端过滤逻辑")
    print("- 不再按时间查询")
    print("- 代码更简洁高效")

if __name__ == "__main__":
    test_simplified_bug_service()
