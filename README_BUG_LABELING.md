# 缺陷自动打标功能

基于大模型的缺陷自动打标功能，能够自动为BUG分配"具体功能"和"问题类型"标签。

## 功能特性

- **智能标签分类**：基于混元大模型自动分析BUG内容，分配合适的标签
- **动态标签管理**：支持复用现有标签，也能创建新标签并自动更新标签库
- **数据过滤**：专门获取健康问问模块且拒绝原因为空的BUG数据
- **Excel导出**：支持导出详细的打标结果和汇总报告
- **持久化存储**：标签库自动保存到本地文件，支持动态更新

## 标签体系

### 问题类型
- 交互逻辑问题/操作冲突
- 交互逻辑问题/交互反馈异常
- 功能逻辑问题/数据未显示
- 功能逻辑问题/跳转逻辑异常
- 界面UI问题/布局遮挡
- 界面UI问题/交互显示异常

### 具体功能
- 智能导诊/性别年龄选择
- 智能导诊/无需细分
- 智能导诊/胸部部位选择
- 智能导诊/入口跳转
- 下滑箭头
- 历史聊天加载
- 页面滚动

## 目录结构

```
backend/app/service/bug_labeling/
├── __init__.py                 # 模块初始化
├── label_manager.py           # 标签管理器
├── labeling_service.py        # 大模型打标服务
├── bug_data_service.py        # BUG数据获取和过滤
└── excel_export_service.py    # Excel导出服务

backend/app/scripts/
├── bug_auto_labeling.py       # 主程序入口
└── test_bug_labeling.py       # 功能测试脚本

# 项目根目录文件
run_bug_labeling.py            # 快速启动脚本
check_config.py                # 配置检查脚本
README_BUG_LABELING.md         # 使用说明文档

# 自动生成的文件和目录
labels.json                    # 标签库文件（自动生成）
exports/                       # 导出文件目录（自动创建）
```

## 快速开始

### 1. 环境检查

首先运行配置检查脚本，确保环境配置正确：

```bash
python check_config.py
```

### 2. 功能测试

运行测试脚本验证功能是否正常：

```bash
python backend/app/scripts/test_bug_labeling.py
```

### 3. 快速启动

使用快速启动脚本（推荐）：

```bash
# 使用默认参数（最近3天，限制50个BUG）
python run_bug_labeling.py

# 自定义参数
python run_bug_labeling.py --days 7 --limit 100
```

## 详细使用方法

### 1. 基本使用

```bash
# 获取最近7天的BUG并进行自动打标
python backend/app/scripts/bug_auto_labeling.py

# 指定时间范围
python backend/app/scripts/bug_auto_labeling.py --start-time 2024-01-01 --end-time 2024-01-31

# 限制处理数量
python backend/app/scripts/bug_auto_labeling.py --limit 50

# 获取最近3天的数据
python backend/app/scripts/bug_auto_labeling.py --days 3
```

### 2. 处理单个BUG

```bash
# 处理指定的BUG
python backend/app/scripts/bug_auto_labeling.py --bug-id 1020375472142465367
```

### 3. 查看标签库

```bash
# 显示当前标签库
python backend/app/scripts/bug_auto_labeling.py --show-labels
```

### 4. 导出选项

```bash
# 不导出Excel文件
python backend/app/scripts/bug_auto_labeling.py --no-excel

# 不导出汇总报告
python backend/app/scripts/bug_auto_labeling.py --no-summary
```

## 编程接口

### 标签管理

```python
from backend.app.service.bug_labeling import label_manager

# 获取所有标签
labels = label_manager.get_all_labels()

# 添加新标签
label_manager.add_problem_type("新问题类型")
label_manager.add_specific_function("新具体功能")

# 检查标签是否存在
exists = label_manager.is_problem_type_exists("交互逻辑问题/操作冲突")
```

### BUG数据获取

```python
from backend.app.service.bug_labeling import bug_data_service

# 获取最近7天的BUG
bugs = bug_data_service.get_recent_bugs(days=7, limit=100)

# 获取指定时间范围的BUG
bugs = bug_data_service.get_bugs_for_labeling(
    start_time="2024-01-01",
    end_time="2024-01-31",
    limit=100
)

# 获取单个BUG
bug = bug_data_service.get_bug_by_id("1020375472142465367")
```

### 自动打标

```python
from backend.app.service.bug_labeling import labeling_service
import asyncio

async def label_bug():
    bug_data = {
        "标题": "智能导诊页面性别选择按钮点击无反应",
        "详细描述": "在智能导诊页面，点击性别选择按钮时没有任何反应",
        "模块": "健康问问"
    }
    
    result = await labeling_service.label_bug(bug_data)
    print(f"问题类型: {result.problem_type}")
    print(f"具体功能: {result.specific_function}")
    print(f"置信度: {result.confidence}")

# 运行
asyncio.run(label_bug())
```

### Excel导出

```python
from backend.app.service.bug_labeling import excel_export_service

# 导出打标结果
results = [...]  # 打标结果列表
excel_file = excel_export_service.export_labeling_results(results)

# 导出汇总报告
summary_file = excel_export_service.export_summary_report(results)
```

## 配置要求

### 环境变量

确保在`.env`文件中配置了以下变量：

```env
# 混元API配置
HUNYUAN_API_URL=http://hunyuanapi.woa.com/openapi/v1
HUNYUAN_API_KEY=your_api_key
HUNYUAN_MODEL=hunyuan-turbos-latest
```

### 依赖包

```bash
pip install pandas openpyxl openai pydantic
```

## 输出文件

### 1. 详细结果Excel文件

包含每个BUG的完整信息和打标结果：
- BUG基本信息（ID、标题、状态等）
- 打标结果（问题类型、具体功能、置信度等）
- 是否为新创建的标签
- 推理过程

### 2. 汇总报告Excel文件

包含多个工作表：
- **汇总统计**：总体统计信息
- **问题类型统计**：各问题类型的分布情况
- **具体功能统计**：各具体功能的分布情况

### 3. 标签库文件（labels.json）

自动维护的标签库文件，包含所有问题类型和具体功能标签。

## 工作流程

1. **数据获取**：从TAPD获取健康问问模块的BUG数据
2. **数据过滤**：过滤出拒绝原因为空且符合条件的BUG
3. **智能打标**：调用大模型分析BUG内容，分配标签
4. **标签管理**：自动更新标签库，记录新创建的标签
5. **结果导出**：生成Excel报告和汇总统计

## 注意事项

1. **API限制**：大模型调用有频率限制，程序会自动添加延迟
2. **数据质量**：确保BUG的标题和描述信息完整
3. **标签一致性**：新创建的标签会自动保存，下次运行时会复用
4. **文件权限**：确保程序有权限创建和写入导出文件

## 故障排除

### 常见问题

1. **API调用失败**：检查混元API配置和网络连接
2. **没有获取到BUG数据**：检查时间范围和过滤条件
3. **Excel导出失败**：检查文件权限和磁盘空间
4. **标签库加载失败**：检查labels.json文件格式

### 日志查看

程序运行时会输出详细的日志信息，可以通过日志定位问题。
