#!/usr/bin/env python3
"""
缺陷自动打标功能配置检查脚本
"""
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def check_python_version():
    """检查Python版本"""
    print("1. Python版本检查")
    print("-" * 30)
    
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 7):
        print("✅ Python版本符合要求 (>= 3.7)")
        return True
    else:
        print("❌ Python版本过低，需要3.7或更高版本")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n2. 依赖包检查")
    print("-" * 30)
    
    required_packages = [
        'pandas',
        'openpyxl', 
        'openai',
        'pydantic',
        'python-dotenv',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包，请安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_env_config():
    """检查环境配置"""
    print("\n3. 环境配置检查")
    print("-" * 30)
    
    # 加载环境变量
    env_file = Path('.env')
    if env_file.exists():
        load_dotenv(env_file)
        print(f"✅ 找到配置文件: {env_file}")
    else:
        print(f"⚠️  未找到.env文件: {env_file}")
    
    # 检查必需的环境变量
    required_vars = {
        'HUNYUAN_API_URL': '混元API地址',
        'HUNYUAN_API_KEY': '混元API密钥',
        'HUNYUAN_MODEL': '混元模型名称'
    }
    
    missing_vars = []
    
    for var, desc in required_vars.items():
        value = os.getenv(var)
        if value:
            # 隐藏敏感信息
            if 'KEY' in var or 'TOKEN' in var:
                display_value = value[:8] + '...' if len(value) > 8 else '***'
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            print(f"❌ {var}: 未设置 ({desc})")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n缺少环境变量，请在.env文件中设置:")
        for var in missing_vars:
            print(f"{var}=your_value_here")
        return False
    
    return True

def check_file_structure():
    """检查文件结构"""
    print("\n4. 文件结构检查")
    print("-" * 30)
    
    required_files = [
        'backend/app/service/bug_labeling/__init__.py',
        'backend/app/service/bug_labeling/label_manager.py',
        'backend/app/service/bug_labeling/labeling_service.py',
        'backend/app/service/bug_labeling/bug_data_service.py',
        'backend/app/service/bug_labeling/excel_export_service.py',
        'backend/app/scripts/bug_auto_labeling.py',
        'backend/app/scripts/test_bug_labeling.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (文件不存在)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺少文件，请检查项目完整性")
        return False
    
    return True

def check_directories():
    """检查并创建必要的目录"""
    print("\n5. 目录结构检查")
    print("-" * 30)
    
    required_dirs = [
        'exports',
        'backend/app/service/bug_labeling'
    ]
    
    for dir_path in required_dirs:
        path = Path(dir_path)
        if path.exists():
            print(f"✅ {dir_path}/")
        else:
            try:
                path.mkdir(parents=True, exist_ok=True)
                print(f"✅ {dir_path}/ (已创建)")
            except Exception as e:
                print(f"❌ {dir_path}/ (创建失败: {e})")
                return False
    
    return True

def main():
    """主函数"""
    print("="*60)
    print("缺陷自动打标功能配置检查")
    print("="*60)
    
    checks = [
        check_python_version,
        check_dependencies,
        check_env_config,
        check_file_structure,
        check_directories
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
        except Exception as e:
            print(f"❌ 检查过程出错: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "="*60)
    print("检查结果总结")
    print("="*60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if all(results):
        print("\n🎉 所有检查通过！可以开始使用缺陷自动打标功能。")
        print("\n快速开始:")
        print("1. 运行测试: python backend/app/scripts/test_bug_labeling.py")
        print("2. 开始打标: python run_bug_labeling.py")
        print("3. 查看帮助: python backend/app/scripts/bug_auto_labeling.py --help")
    else:
        print(f"\n⚠️  有 {total - passed} 项检查未通过，请根据上述提示进行修复。")
        
        if not results[1]:  # 依赖包检查失败
            print("\n安装依赖包:")
            print("pip install pandas openpyxl openai pydantic python-dotenv requests")
        
        if not results[2]:  # 环境配置检查失败
            print("\n配置环境变量:")
            print("1. 复制 backend/.env_template 为 .env")
            print("2. 在 .env 文件中填入正确的配置值")

if __name__ == "__main__":
    main()
