#!/usr/bin/env python3
"""
缺陷自动打标主程序
整合所有功能模块，实现完整的缺陷自动打标流程
"""
import os
import sys
import asyncio
import argparse
import json
from datetime import datetime
from typing import Dict
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from backend.app.service.bug_labeling.bug_data_service import bug_data_service
from backend.app.service.bug_labeling.labeling_service import labeling_service
from backend.app.service.bug_labeling.excel_export_service import excel_export_service
from backend.app.service.bug_labeling.label_manager import label_manager
from backend.app.utils.logger_util import logger


class BugAutoLabelingManager:
    """缺陷自动打标管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.bug_data_service = bug_data_service
        self.labeling_service = labeling_service
        self.excel_export_service = excel_export_service
        self.label_manager = label_manager
        
        # 统计信息
        self.stats = {
            'total_bugs': 0,
            'success_count': 0,
            'error_count': 0,
            'new_problem_types': 0,
            'new_specific_functions': 0,
            'start_time': None,
            'end_time': None
        }
    
    async def run_auto_labeling(
        self,
        limit: int = 100,
        export_excel: bool = True,
        export_summary: bool = True
    ) -> Dict:
        """
        运行自动打标流程

        Args:
            limit: 最大处理数量
            export_excel: 是否导出Excel
            export_summary: 是否导出汇总报告

        Returns:
            Dict: 执行结果
        """
        try:
            self.stats['start_time'] = datetime.now()
            logger.info("开始执行缺陷自动打标流程")

            # 1. 获取并逐个处理BUG数据
            logger.info("步骤1: 逐个获取和处理BUG数据")

            results = []
            bug_count = 0

            # 逐个处理BUG
            for bug_data in self.bug_data_service.get_bugs_for_labeling(limit=limit):
                bug_count += 1
                logger.info(f"开始打标第 {bug_count} 个BUG: {bug_data.get('标题', '未知标题')}")
                filter_bug_data = {k:v for k, v in bug_data.items() if v != None and v != ""}
                try:
                    # 执行打标
                    labeling_result = await self.labeling_service.label_bug(filter_bug_data)

                    # 统计结果
                    if labeling_result.error_message:
                        self.stats['error_count'] += 1
                        logger.error(f"BUG打标失败: {labeling_result.error_message}")
                    else:
                        self.stats['success_count'] += 1
                        if labeling_result.is_new_problem_type:
                            self.stats['new_problem_types'] += 1
                        if labeling_result.is_new_specific_function:
                            self.stats['new_specific_functions'] += 1

                    # 保存结果
                    results.append({
                        'bug_data': bug_data,
                        'labeling_result': labeling_result.dict() if hasattr(labeling_result, 'dict') else labeling_result.__dict__
                    })

                    logger.info(f"BUG打标完成: 问题类型={labeling_result.problem_type}, 具体功能={labeling_result.specific_function}")

                    # 添加延迟，避免API调用过于频繁
                    await asyncio.sleep(1)

                except Exception as e:
                    logger.error(f"处理BUG失败: {str(e)}")
                    self.stats['error_count'] += 1
                    results.append({
                        'bug_data': bug_data,
                        'labeling_result': {
                            'error_message': f'处理失败: {str(e)}',
                            'problem_type': '',
                            'specific_function': '',
                            'confidence': 0.0,
                            'is_new_problem_type': False,
                            'is_new_specific_function': False,
                            'reasoning': ''
                        }
                    })

            if bug_count == 0:
                logger.warning("未获取到符合条件的BUG数据")
                return {
                    'success': False,
                    'message': '未获取到符合条件的BUG数据',
                    'stats': self.stats
                }

            self.stats['total_bugs'] = bug_count
            logger.info(f"完成 {bug_count} 个BUG的处理和打标")
            
            # 3. 导出结果
            export_files = []
            if export_excel and results:
                logger.info("步骤3: 导出Excel文件")
                excel_file = self.excel_export_service.export_labeling_results(
                    results, 
                    include_raw_data=True
                )
                export_files.append(excel_file)
                
                if export_summary:
                    summary_file = self.excel_export_service.export_summary_report(results)
                    export_files.append(summary_file)
            
            # 4. 更新统计信息
            self.stats['end_time'] = datetime.now()
            duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            # 5. 输出结果
            result = {
                'success': True,
                'message': f'自动打标完成，共处理 {self.stats["total_bugs"]} 个BUG',
                'stats': {
                    **self.stats,
                    'duration_seconds': duration,
                    'success_rate': f"{self.stats['success_count']/self.stats['total_bugs']*100:.1f}%" if self.stats['total_bugs'] > 0 else "0%"
                },
                'export_files': export_files,
                'label_summary': self.label_manager.get_labels_summary()
            }
            
            logger.info(f"自动打标流程完成: {result['message']}")
            return result
            
        except Exception as e:
            logger.error(f"自动打标流程失败: {str(e)}")
            return {
                'success': False,
                'message': f'自动打标流程失败: {str(e)}',
                'stats': self.stats
            }
    

    
    async def process_single_bug(self, bug_id: str) -> Dict:
        """处理单个BUG"""
        try:
            logger.info(f"开始处理单个BUG: {bug_id}")
            
            # 获取BUG数据
            bug_data = self.bug_data_service.get_bug_by_id(bug_id)
            if not bug_data:
                return {
                    'success': False,
                    'message': f'未找到BUG或BUG不符合打标条件: {bug_id}'
                }
            
            # 执行打标
            labeling_result = await self.labeling_service.label_bug(bug_data)
            
            return {
                'success': True,
                'bug_data': bug_data,
                'labeling_result': labeling_result.dict() if hasattr(labeling_result, 'dict') else labeling_result.__dict__
            }
            
        except Exception as e:
            logger.error(f"处理单个BUG失败: {str(e)}")
            return {
                'success': False,
                'message': f'处理失败: {str(e)}'
            }
    
    def get_label_statistics(self) -> Dict:
        """获取标签统计信息"""
        return {
            'current_labels': self.label_manager.get_all_labels(),
            'label_counts': self.label_manager.get_labels_summary()
        }


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='缺陷自动打标工具')
    parser.add_argument('--limit', type=int, default=100, help='最大处理数量 (默认100)')
    parser.add_argument('--bug-id', help='处理单个BUG (指定BUG ID)')
    parser.add_argument('--no-excel',default=False, action='store_true', help='不导出Excel文件')
    parser.add_argument('--no-summary', default=False, action='store_true', help='不导出汇总报告')
    parser.add_argument('--show-labels', default=False, action='store_true', help='显示当前标签库')

    args = parser.parse_args()

    manager = BugAutoLabelingManager()

    # 显示标签库
    if args.show_labels:
        labels = manager.get_label_statistics()
        print("当前标签库:")
        print(json.dumps(labels, ensure_ascii=False, indent=2))
        return

    # 处理单个BUG
    if args.bug_id:
        result = await manager.process_single_bug(args.bug_id)
        print(json.dumps(result, ensure_ascii=False, indent=2))
        return

    # 执行自动打标
    result = await manager.run_auto_labeling(
        limit=args.limit,
        export_excel=not args.no_excel,
        export_summary=not args.no_summary
    )

    # 输出结果
    print("\n" + "="*50)
    print("缺陷自动打标执行结果")
    print("="*50)
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    asyncio.run(main())
