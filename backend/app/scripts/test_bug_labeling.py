#!/usr/bin/env python3
"""
缺陷自动打标功能测试脚本
用于验证各个模块是否正常工作
"""
import os
import sys
import asyncio
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

from backend.app.service.bug_labeling import (
    label_manager,
    labeling_service, 
    bug_data_service,
    excel_export_service
)
from backend.app.utils.logger_util import logger


class BugLabelingTester:
    """缺陷自动打标功能测试器"""
    
    def __init__(self):
        self.test_results = []
    
    def run_all_tests(self):
        """运行所有测试"""
        print("="*60)
        print("缺陷自动打标功能测试")
        print("="*60)
        
        # 测试标签管理器
        self.test_label_manager()
        
        # 测试BUG数据服务
        self.test_bug_data_service()
        
        # 测试大模型打标服务
        asyncio.run(self.test_labeling_service())
        
        # 测试Excel导出服务
        self.test_excel_export_service()
        
        # 输出测试结果
        self.print_test_summary()
    
    def test_label_manager(self):
        """测试标签管理器"""
        print("\n1. 测试标签管理器")
        print("-" * 30)
        
        try:
            # 测试获取标签
            problem_types = label_manager.get_problem_types()
            specific_functions = label_manager.get_specific_functions()
            
            print(f"✓ 问题类型数量: {len(problem_types)}")
            print(f"✓ 具体功能数量: {len(specific_functions)}")
            
            # 测试添加标签
            test_problem_type = "测试问题类型_" + str(hash("test"))[-6:]
            test_function = "测试具体功能_" + str(hash("test"))[-6:]
            
            success1 = label_manager.add_problem_type(test_problem_type)
            success2 = label_manager.add_specific_function(test_function)
            
            if success1 and success2:
                print("✓ 标签添加功能正常")
            else:
                print("✗ 标签添加功能异常")
            
            # 测试标签存在性检查
            exists1 = label_manager.is_problem_type_exists(test_problem_type)
            exists2 = label_manager.is_specific_function_exists(test_function)
            
            if exists1 and exists2:
                print("✓ 标签存在性检查正常")
            else:
                print("✗ 标签存在性检查异常")
            
            self.test_results.append(("标签管理器", True, "所有功能正常"))
            
        except Exception as e:
            print(f"✗ 标签管理器测试失败: {str(e)}")
            self.test_results.append(("标签管理器", False, str(e)))
    
    def test_bug_data_service(self):
        """测试BUG数据服务"""
        print("\n2. 测试BUG数据服务")
        print("-" * 30)
        
        try:
            # 测试获取最近的BUG数据
            bugs = bug_data_service.get_recent_bugs(days=1, limit=5)
            print(f"✓ 获取到 {len(bugs)} 个最近的BUG")
            
            if bugs:
                # 显示第一个BUG的基本信息
                first_bug = bugs[0]
                print(f"  示例BUG ID: {first_bug.get('ID', 'N/A')}")
                print(f"  示例BUG标题: {first_bug.get('标题', 'N/A')[:50]}...")
                print(f"  示例BUG模块: {first_bug.get('模块', 'N/A')}")
            
            # 测试统计信息
            stats = bug_data_service.get_statistics()
            if stats:
                print(f"✓ 统计信息获取正常，总数量: {stats.get('总数量', 0)}")
            
            self.test_results.append(("BUG数据服务", True, f"获取到{len(bugs)}个BUG"))
            
        except Exception as e:
            print(f"✗ BUG数据服务测试失败: {str(e)}")
            self.test_results.append(("BUG数据服务", False, str(e)))
    
    async def test_labeling_service(self):
        """测试大模型打标服务"""
        print("\n3. 测试大模型打标服务")
        print("-" * 30)
        
        try:
            # 创建测试BUG数据
            test_bug = {
                "ID": "test_123456",
                "标题": "智能导诊页面性别选择按钮点击无反应",
                "详细描述": "在智能导诊页面，用户点击性别选择按钮时没有任何反应，无法进行下一步操作。测试环境：iOS 15.0，Safari浏览器。",
                "模块": "健康问问",
                "状态": "新建",
                "创建人": "测试用户",
                "优先级": "中",
                "严重程度": "一般"
            }
            
            print("正在调用大模型进行打标...")
            result = await labeling_service.label_bug(test_bug)
            
            if result.error_message:
                print(f"✗ 打标失败: {result.error_message}")
                self.test_results.append(("大模型打标服务", False, result.error_message))
            else:
                print(f"✓ 打标成功")
                print(f"  问题类型: {result.problem_type}")
                print(f"  具体功能: {result.specific_function}")
                print(f"  置信度: {result.confidence:.2f}")
                print(f"  是否新问题类型: {'是' if result.is_new_problem_type else '否'}")
                print(f"  是否新具体功能: {'是' if result.is_new_specific_function else '否'}")
                
                self.test_results.append(("大模型打标服务", True, "打标成功"))
            
        except Exception as e:
            print(f"✗ 大模型打标服务测试失败: {str(e)}")
            self.test_results.append(("大模型打标服务", False, str(e)))
    
    def test_excel_export_service(self):
        """测试Excel导出服务"""
        print("\n4. 测试Excel导出服务")
        print("-" * 30)
        
        try:
            # 创建测试数据
            test_results = [
                {
                    'bug_data': {
                        'ID': 'test_123456',
                        '标题': '测试BUG标题',
                        '状态': '新建',
                        '创建人': '测试用户',
                        '模块': '健康问问',
                        '优先级': '中',
                        '严重程度': '一般',
                        '创建时间': '2024-01-01 10:00:00'
                    },
                    'labeling_result': {
                        'problem_type': '交互逻辑问题/操作冲突',
                        'specific_function': '智能导诊/性别年龄选择',
                        'confidence': 0.85,
                        'is_new_problem_type': False,
                        'is_new_specific_function': False,
                        'reasoning': '测试推理过程',
                        'error_message': ''
                    }
                }
            ]
            
            # 测试导出详细结果
            excel_file = excel_export_service.export_labeling_results(
                test_results, 
                filename="test_labeling_results.xlsx"
            )
            print(f"✓ 详细结果导出成功: {excel_file}")
            
            # 测试导出汇总报告
            summary_file = excel_export_service.export_summary_report(
                test_results,
                filename="test_summary_report.xlsx"
            )
            print(f"✓ 汇总报告导出成功: {summary_file}")
            
            self.test_results.append(("Excel导出服务", True, "导出功能正常"))
            
        except Exception as e:
            print(f"✗ Excel导出服务测试失败: {str(e)}")
            self.test_results.append(("Excel导出服务", False, str(e)))
    
    def print_test_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("测试结果总结")
        print("="*60)
        
        passed_count = 0
        failed_count = 0
        
        for test_name, success, message in self.test_results:
            status = "✓ 通过" if success else "✗ 失败"
            print(f"{test_name:<20} {status:<10} {message}")
            
            if success:
                passed_count += 1
            else:
                failed_count += 1
        
        print("-" * 60)
        print(f"总计: {len(self.test_results)} 项测试")
        print(f"通过: {passed_count} 项")
        print(f"失败: {failed_count} 项")
        
        if failed_count == 0:
            print("\n🎉 所有测试通过！缺陷自动打标功能可以正常使用。")
        else:
            print(f"\n⚠️  有 {failed_count} 项测试失败，请检查相关配置和依赖。")
        
        print("\n使用说明:")
        print("1. 运行完整的自动打标流程:")
        print("   python backend/app/scripts/bug_auto_labeling.py")
        print("\n2. 查看详细使用方法:")
        print("   python backend/app/scripts/bug_auto_labeling.py --help")
        print("\n3. 查看README文档:")
        print("   cat README_BUG_LABELING.md")


def main():
    """主函数"""
    tester = BugLabelingTester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
