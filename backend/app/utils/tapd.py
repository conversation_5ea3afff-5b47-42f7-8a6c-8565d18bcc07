import json

from bs4 import BeautifulSoup
import html2text
from markdownify import markdownify as md
from typing import List, Dict
from backend.app.config.config import BUG_EVALUATION_WORKSPACE_ID, DESCRIPTION_MAX_LENGTH, JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID, YIYAO_SAAS_WORKSPACE_ID, MIYING_WORKSPACE_ID, DAOZHEN_WORKSPACE_ID
from dataclasses import dataclass, field
from typing import List, Dict
import requests
from requests.exceptions import RequestException
from html2text import HTML2Text
import re
from backend.app.utils.logger_util import logger

class HTMLProcessor:
    def __init__(self, excluded_tags=None, markdown_config=None):
        self.excluded_tags = excluded_tags or ['script', 'style', 'nav']
        self.markdown_config = markdown_config or {'body_width': 0}

    def clean_html(self, html):
        """清理指定标签的HTML文档"""
        soup = BeautifulSoup(html, 'html.parser')
        for tag in soup(self.excluded_tags):
            tag.decompose()
        return str(soup)

    def convert_to_markdown(self, html):
        """转换为Markdown格式"""
        converter = html2text.HTML2Text()
        converter.body_width = self.markdown_config.get('body_width', 0)
        return converter.handle(self.clean_html(html))


def extract_video_links(html: str) -> str:
    def replacer(match):
        src = match.group(1)
        return f"\n[🎥 视频预览]({src})\n"

    # 提取 <video src="..."> 或 <source src="...">
    html = re.sub(r'<video[^>]+src="([^"]+)"[^>]*>.*?</video>', replacer, html, flags=re.DOTALL)
    html = re.sub(r'<source[^>]+src="([^"]+)"[^>]*>', replacer, html)
    return html

def md_with_fix(html: str) -> str:
    html = extract_video_links(html)  # 提取视频链接为 Markdown 格式

    html = re.sub(r'</div>', '</div>\n', html)
    html = re.sub(r'</p>', '</p>\n', html)

    h = HTML2Text()
    h.body_width = 0
    h.ignore_images = False
    h.ignore_links = False
    h.ignore_emphasis = True  # 关键：忽略 <i>/<em> 斜体转换
    return h.handle(html).strip()

def bug_html2md(bugs_data: dict, *args):
    for arg in args:
        if isinstance(arg, str) and arg in bugs_data and isinstance(bugs_data[arg], str):
            bugs_data[arg] = md_with_fix(bugs_data[arg])


def parse_description_to_json(description: str, workspace_id: str) -> dict:
    """
    根据不同业务模板，使用正则匹配将详细描述转化为JSON格式

    Args:
        description: 详细描述字段内容
        workspace_id: 工作区ID，用于确定业务模板

    Returns:
        dict: 解析后的JSON格式数据
    """
    if not description or not isinstance(description, str):
        return {}

    # 转换HTML为Markdown
    description_md = md_with_fix(description)
    print("description_md" + description_md)
    # 根据workspace_id确定是否需要细粒度解析
    workspace_id = str(workspace_id)

    # 对医药SaaS进行细粒度解析
    if workspace_id == str(YIYAO_SAAS_WORKSPACE_ID):
        return parse_yiyao_saas_description(description_md)
    else:
        return parse_standard_description(description_md, workspace_id)





def parse_standard_description(description_md: str, workspace_id: str) -> dict:
    """
    标准模板解析（保留换行符）
    """
    field_patterns = get_field_patterns_by_workspace(workspace_id)
    result = {}

    # 直接使用正则匹配提取字段内容
    for field_name, pattern in field_patterns.items():
        match = re.search(pattern, description_md, re.DOTALL | re.IGNORECASE)
        if match:
            content = match.group(1).strip()
            # 保持原有的换行格式，只清理多余的空行
            content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
            # 只清理每行开头和结尾的空格，但保留换行符
            lines = content.split('\n')
            lines = [line.strip() for line in lines]
            content = '\n'.join(lines)
            if content:
                result[field_name] = content
            else:
                # 如果内容为空，尝试更宽松的匹配（特别针对【实际结果】字段）
                if field_name == "【实际结果】":
                    # 尝试更宽松的匹配模式
                    loose_pattern = r"【实际结果】[^】]*】?\s*(.*?)(?=【|$)"
                    loose_match = re.search(loose_pattern, description_md, re.DOTALL | re.IGNORECASE)
                    if loose_match:
                        content = loose_match.group(1).strip()
                        # 保持原有的换行格式，只清理多余的空行
                        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
                        # 只清理每行开头和结尾的空格，但保留换行符
                        lines = content.split('\n')
                        lines = [line.strip() for line in lines]
                        content = '\n'.join(lines)
                        if content:
                            result[field_name] = content
        else:
            # 如果标准正则匹配失败，尝试更宽松的匹配（特别针对【实际结果】字段）
            if field_name == "【实际结果】":
                # 尝试更宽松的匹配模式
                loose_pattern = r"【实际结果】[^】]*】?\s*(.*?)(?=【|$)"
                loose_match = re.search(loose_pattern, description_md, re.DOTALL | re.IGNORECASE)
                if loose_match:
                    content = loose_match.group(1).strip()
                    # 保持原有的换行格式，只清理多余的空行
                    content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
                    # 只清理每行开头和结尾的空格，但保留换行符
                    lines = content.split('\n')
                    lines = [line.strip() for line in lines]
                    content = '\n'.join(lines)
                    if content:
                        result[field_name] = content

    return result


def parse_yiyao_saas_description(description_md: str) -> dict:
    """
    医药SaaS模板细粒度解析
    """
    result = {}

    # 主要字段模板 - 修复：使用更精确的字段边界匹配
    main_fields = {
        "【前提条件】": r"【前提条件】\s*[：:]*\s*(.*?)(?=【(?:问题现象|重现步骤|预期结果|其他补充)】|$)",
        "【问题现象】": r"【问题现象】\s*[：:]*\s*(.*?)(?=【(?:前提条件|重现步骤|预期结果|其他补充)】|$)",
        "【重现步骤】": r"【重现步骤】\s*[：:]*\s*(.*?)(?=【(?:前提条件|问题现象|预期结果|其他补充)】|$)",
        "【预期结果】": r"【预期结果】\s*[：:]*\s*(.*?)(?=【(?:前提条件|问题现象|重现步骤|其他补充)】|$)",
        "【其他补充】": r"【其他补充】\s*[：:]*\s*(.*?)(?=【(?:前提条件|问题现象|重现步骤|预期结果)】|$)"
    }

    # 直接使用正则匹配提取字段内容
    for field_name, pattern in main_fields.items():
        match = re.search(pattern, description_md, re.DOTALL | re.IGNORECASE)
        if match:
            content = match.group(1).strip()

            # 对特定字段进行细粒度解析
            if field_name == "【前提条件】":
                result[field_name] = parse_yiyao_prerequisite(content)
            elif field_name == "【其他补充】":
                result[field_name] = parse_yiyao_supplement(content)
            elif field_name == "【问题现象】":
                parsed_content = parse_yiyao_problem_description(content)
                result[field_name] = parsed_content
            else:
                # 保持原有的换行格式，只清理多余的空行
                content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
                # 只清理每行开头和结尾的空格，但保留换行符
                lines = content.split('\n')
                lines = [line.strip() for line in lines]
                content = '\n'.join(lines)
                result[field_name] = content

    return result


def parse_yiyao_prerequisite(content: str) -> dict:
    """
    解析医药SaaS前提条件的细粒度字段
    """
    result = {}

    # 子字段模式 - 修改为匹配到下一个字段标题之前的所有内容
    sub_patterns = {
        "设备机型": r"设备机型\s*[：:]\s*(.*?)(?=登录账号[/／]角色信息|问题发生时间|$)",
        "登录账号/角色信息": r"登录账号[/／]角色信息\s*[：:]\s*(.*?)(?=设备机型|问题发生时间|$)",
        "问题发生时间": r"问题发生时间\s*[：:]\s*(.*?)(?=设备机型|登录账号[/／]角色信息|$)"
    }

    for field_name, pattern in sub_patterns.items():
        match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
        if match:
            field_content = match.group(1).strip()
            # 保持原有的换行格式，只清理多余的空行
            field_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', field_content)
            # 只清理每行开头和结尾的空格，但保留换行符
            lines = field_content.split('\n')
            lines = [line.strip() for line in lines]
            field_content = '\n'.join(lines)
            if field_content:
                result[field_name] = field_content

    return result


def parse_yiyao_supplement(content: str) -> dict:
    """
    解析医药SaaS其他补充的细粒度字段
    """
    result = {}

    # 子字段模式（包括截图/录屏）- 修改为匹配到下一个字段标题之前的所有内容
    sub_patterns = {
        "入口or菜单": r"入口or菜单\s*[：:]\s*(.*?)(?=日志信息|抓包信息|截图[/／]录屏|$)",
        "日志信息": r"日志信息\s*[：:]\s*(.*?)(?=入口or菜单|抓包信息|截图[/／]录屏|$)",
        "抓包信息": r"抓包信息\s*[：:]\s*(.*?)(?=入口or菜单|日志信息|截图[/／]录屏|$)",
        "截图/录屏": r"截图[/／]录屏\s*[：:]\s*(.*?)(?=入口or菜单|日志信息|抓包信息|$)"
    }

    for field_name, pattern in sub_patterns.items():
        match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
        if match:
            field_content = match.group(1).strip()
            # 保持原有的换行格式，只清理多余的空行
            field_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', field_content)
            # 只清理每行开头和结尾的空格，但保留换行符
            lines = field_content.split('\n')
            lines = [line.strip() for line in lines]
            field_content = '\n'.join(lines)
            if field_content:
                result[field_name] = field_content

    # 如果没有匹配到任何子字段，将整个内容作为补充信息返回
    if not result and content.strip():
        # 保持原有的换行格式，只清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        # 只清理每行开头和结尾的空格，但保留换行符
        lines = content.split('\n')
        lines = [line.strip() for line in lines]
        content = '\n'.join(lines)
        result["补充信息"] = content

    return result


def parse_yiyao_problem_description(content: str) -> dict:
    """
    解析医药SaaS问题现象字段，提取编号内容和截图/录屏
    """
    result = {}

    # 先提取截图/录屏部分
    screenshot_match = re.search(r'截图[/／]录屏\s*[：:]\s*(.*?)(?=\n\d+[\.、]|$)', content, re.IGNORECASE | re.DOTALL)
    if screenshot_match:
        result["截图/录屏"] = screenshot_match.group(1).strip()
        # 从内容中移除截图/录屏部分，避免重复
        content = re.sub(r'截图[/／]录屏\s*[：:]\s*.*?(?=\n\d+[\.、]|$)', '', content, flags=re.IGNORECASE | re.DOTALL)

    # 提取编号内容，保持原有换行格式
    if content.strip():
        # 只清理多余的空行
        content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
        # 只清理每行开头和结尾的空格，但保留换行符
        lines = content.split('\n')
        lines = [line.strip() for line in lines]
        content = '\n'.join(lines)
        result["问题描述"] = content

    return result





def get_field_patterns_by_workspace(workspace_id: str) -> dict:
    """
    根据workspace_id返回对应的字段正则匹配模式

    注意：正则模式中使用 \s* 来匹配字段名后的空白字符（包括换行符）
    修复：使用更精确的字段边界匹配，避免内容中的【】符号干扰解析

    Args:
        workspace_id: 工作区ID

    Returns:
        dict: 字段名到正则模式的映射
    """
    workspace_id = str(workspace_id)

    # JIANKANG 和 YIBAO 使用相同模板
    if workspace_id in [str(JIANKANG_WORKSPACE_ID), str(YIBAO_WORKSPACE_ID)]:
        return {
            "【条件】": r"【条件】\s*[：:]*\s*(.*?)(?=【(?:机型|步骤|预期结果|实际结果)】|$)",
            "【机型】": r"【机型】\s*[：:]*\s*(.*?)(?=【(?:条件|步骤|预期结果|实际结果)】|$)",
            "【步骤】": r"【步骤】\s*[：:]*\s*(.*?)(?=【(?:条件|机型|预期结果|实际结果)】|$)",
            "【预期结果】": r"【预期结果】\s*[：:]*\s*(.*?)(?=【(?:条件|机型|步骤|实际结果)】|$)",
            "【实际结果】": r"【实际结果】\s*[：:]*\s*(.*?)(?=【(?:条件|机型|步骤|预期结果)】|$)"
        }

    # YIYAO_SAAS 模板
    elif workspace_id == str(YIYAO_SAAS_WORKSPACE_ID):
        return {
            "【前提条件】": r"【前提条件】\s*[：:]*\s*(.*?)(?=【(?:问题现象|重现步骤|预期结果|其他补充)】|$)",
            "【问题现象】": r"【问题现象】\s*[：:]*\s*(.*?)(?=【(?:前提条件|重现步骤|预期结果|其他补充)】|$)",
            "【重现步骤】": r"【重现步骤】\s*[：:]*\s*(.*?)(?=【(?:前提条件|问题现象|预期结果|其他补充)】|$)",
            "【预期结果】": r"【预期结果】\s*[：:]*\s*(.*?)(?=【(?:前提条件|问题现象|重现步骤|其他补充)】|$)",
            "【其他补充】": r"【其他补充】\s*[：:]*\s*(.*?)(?=【(?:前提条件|问题现象|重现步骤|预期结果)】|$)"
        }

    # MIYING 模板
    elif workspace_id == str(MIYING_WORKSPACE_ID):
        return {
            "【环境】": r"【环境】\s*[：:]*\s*(.*?)(?=【(?:前置条件|机型|步骤|现象|重现规律|预期结果)】|$)",
            "【前置条件】": r"【前置条件】\s*[：:]*\s*(.*?)(?=【(?:环境|机型|步骤|现象|重现规律|预期结果)】|$)",
            "【机型】": r"【机型】\s*[：:]*\s*(.*?)(?=【(?:环境|前置条件|步骤|现象|重现规律|预期结果)】|$)",
            "【步骤】": r"【步骤】\s*[：:]*\s*(.*?)(?=【(?:环境|前置条件|机型|现象|重现规律|预期结果)】|$)",
            "【现象】": r"【现象】\s*[：:]*\s*(.*?)(?=【(?:环境|前置条件|机型|步骤|重现规律|预期结果)】|$)",
            "【重现规律】": r"【重现规律】\s*[：:]*\s*(.*?)(?=【(?:环境|前置条件|机型|步骤|现象|预期结果)】|$)",
            "【预期结果】": r"【预期结果】\s*[：:]*\s*(.*?)(?=【(?:环境|前置条件|机型|步骤|现象|重现规律)】|$)"
        }

    # DAOZHEN 模板
    elif workspace_id == str(DAOZHEN_WORKSPACE_ID):
        return {
            "【测试地址】": r"【测试地址】\s*[：:]*\s*(.*?)(?=【(?:机型|步骤|现象|重现规律|预期结果)】|$)",
            "【机型】": r"【机型】\s*[：:]*\s*(.*?)(?=【(?:测试地址|步骤|现象|重现规律|预期结果)】|$)",
            "【步骤】": r"【步骤】\s*[：:]*\s*(.*?)(?=【(?:测试地址|机型|现象|重现规律|预期结果)】|$)",
            "【现象】": r"【现象】\s*[：:]*\s*(.*?)(?=【(?:测试地址|机型|步骤|重现规律|预期结果)】|$)",
            "【重现规律】": r"【重现规律】\s*[：:]*\s*(.*?)(?=【(?:测试地址|机型|步骤|现象|预期结果)】|$)",
            "【预期结果】": r"【预期结果】\s*[：:]*\s*(.*?)(?=【(?:测试地址|机型|步骤|现象|重现规律)】|$)"
        }

    # 默认使用 JIANKANG 模板
    else:
        return {
            "【条件】": r"【条件】\s*[：:]*\s*(.*?)(?=【(?:机型|步骤|预期结果|实际结果)】|$)",
            "【机型】": r"【机型】\s*[：:]*\s*(.*?)(?=【(?:条件|步骤|预期结果|实际结果)】|$)",
            "【步骤】": r"【步骤】\s*[：:]*\s*(.*?)(?=【(?:条件|机型|预期结果|实际结果)】|$)",
            "【预期结果】": r"【预期结果】\s*[：:]*\s*(.*?)(?=【(?:条件|机型|步骤|实际结果)】|$)",
            "【实际结果】": r"【实际结果】\s*[：:]*\s*(.*?)(?=【(?:条件|机型|步骤|预期结果)】|$)"
        }

class TAPDClient:
    def __init__(self, base_url='http://apiv2.tapd.woa.com', auth_token=None, workspace_id=None):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Basic {auth_token}' if auth_token else ''
        }
        self.fields_info = self.get_fields_info(workspace_id)
        self.fields_label = self.get_fields_lable(workspace_id)
        self.workspace_id = workspace_id

    def get_story_name(self, workspace_id, story_id):
        """获取 tapd需求名称"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'name'
        }
        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data.get('name')
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_story(self, workspace_id, story_id):
        """获取 tapd需求全部信息"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'id,name,status,owner,description'
        }
        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_story_description(self, workspace_id, story_id):
        """获取 tapd需求详情"""
        params = {
            'workspace_id': workspace_id,
            'id': story_id,
            'fields': 'description'
        }

        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_data = data.get('data', [{}])[0].get('Story', {})
            return story_data.get('description')
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_image(self, workspace_id, image_path):
        """
        获取TAPD图片
        参数:
            workspace_id: 工作区ID
            image_path: 图片路径(如/tfl/captures/2023-07/tapd_10104801_base64_1689686020_146.png)
        返回:
            requests.Response对象
        """
        params = {
            'workspace_id': workspace_id,
            'image_path': image_path
        }

        try:
            response = requests.get(
                f"{self.base_url}/files/get_image",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            response = response.json()
            download_url = response.get('data', {}).get('Attachment', {}).get('download_url', "")
            return download_url
        except RequestException as e:
            raise TAPDError(f"获取图片失败: {str(e)}") from e
        
    def get_stories_by_ids(self, workspace_id, ids):

        """
        通过workspace_id和ids获取tapd需求详情
        """
        params = {
            'workspace_id': workspace_id,
            "id": ",".join(map(str, ids))
            }
        try:
            response = requests.get(
                f"{self.base_url}/stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            #[{'Story': {...}}, {'Story': {...}}]
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e
        

    def get_bug_by_id(self, workspace_id, bug_id):
        """
        通过workspace_id，bug_id获取tapd缺项详情
        """
        params = {
            'workspace_id': workspace_id,
            'id': bug_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            if data['data'] == []:
                return {}
            bug_data = data.get('data', [{}])[0].get('Bug', {})
            #filtered_bug_data = {k: v for k, v in bug_data.items() if v != "" and v is not None}
            return bug_data
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_custom_fields_settings(self, workspace_id) -> List[Dict]:

        params = {
            'workspace_id': workspace_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/custom_fields_settings",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            custom_fields_config = data.get('data', [{}])
            # filtered_bug_data = {k: v for k, v in bug_data.items() if v != "" and v is not None}
            return custom_fields_config
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def update_bug(self, workspace_id, bug_id, description):
        data = {
            'workspace_id': workspace_id,
            'id': bug_id,
            'description': description
        }
        try:
            response = requests.post(
                f"{self.base_url}/bugs",
                headers=self.headers,
                data=data,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def update_bug_title(self, workspace_id, bug_id, title):
        """
        更新缺陷标题

        Args:
            workspace_id: 工作空间ID
            bug_id: 缺陷ID
            title: 新的标题

        Raises:
            TAPDError: 当API调用失败时抛出异常
        """
        # 确保参数类型正确
        request_data = {
            'workspace_id': str(workspace_id),
            'id': str(bug_id),
            'title': str(title)
        }

        # 添加调试日志
        logger.info(f"准备更新TAPD缺陷标题，参数: workspace_id={workspace_id}, bug_id={bug_id}, title={title}")

        try:
            response = requests.post(
                f"{self.base_url}/bugs",
                headers=self.headers,
                data=request_data,
                timeout=10
            )

            # 记录响应状态
            logger.info(f"TAPD API响应状态码: {response.status_code}")

            response.raise_for_status()
            response_data = response.json()

            # 记录响应内容
            logger.info(f"TAPD API响应内容: {response_data}")

            if response_data.get('status') != 1:
                error_info = response_data.get('info', '未知错误')
                raise TAPDError(f"API返回异常状态码: {response_data.get('status')}, 信息: {error_info}")

            return response_data

        except requests.exceptions.HTTPError as e:
            # 记录详细的HTTP错误信息
            logger.error(f"HTTP错误: {e}")
            if hasattr(e.response, 'text'):
                logger.error(f"响应内容: {e.response.text}")
            raise TAPDError(f"HTTP请求失败: {str(e)}") from e
        except RequestException as e:
            logger.error(f"请求异常: {e}")
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def get_fields_lable(self, workspace_id) -> List[Dict]:
        params = {
            'workspace_id': workspace_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/get_fields_lable",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            custom_fields_config = data.get('data', {})
            # filtered_bug_data = {k: v for k, v in bug_data.items() if v != "" and v is not None}
            return custom_fields_config
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def replace_custom_fields(self, bug_data: dict, custom_fields_config):
        # 生成字段映射字典：{custom_field -> name}
        field_mapping = {
            cfg['CustomFieldConfig']['custom_field']: cfg['CustomFieldConfig']['name']
            for cfg in custom_fields_config
            if cfg['CustomFieldConfig']['enabled'] == "1"  # 只处理启用状态的配置
        }

        # 创建新字典（保留原始非自定义字段，替换已配置的自定义字段）
        new_bug_data = {}
        for key, value in bug_data.items():
            if key in field_mapping:
                # 替换自定义字段名称
                new_key = field_mapping[key]
            else:
                # 保留非自定义字段
                new_key = key

            # 避免键冲突（如果多个custom_field映射到同一name）
            if new_key in new_bug_data:
                if isinstance(new_bug_data[new_key], list):
                    new_bug_data[new_key].append(value)
                else:
                    new_bug_data[new_key] = [new_bug_data[new_key], value]
            else:
                new_bug_data[new_key] = value

        return new_bug_data
    
    def get_bugs(self, workspace_id, limit=200, page=1) -> List[Dict]:
        params = {
            'workspace_id': workspace_id,
            'limit': limit,
            'page': page,
            'order': 'created desc',
        }
        try:
            response = requests.get(f"{self.base_url}/bugs", headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def get_bugs_by_time_range(self, workspace_id, start_time=None, end_time=None, limit=200, page=1) -> List[Dict]:
        """
        根据时间范围获取缺陷列表

        Args:
            workspace_id: 工作区ID
            start_time: 开始时间，格式：'2024-01-01 00:00:00' 或 '2024-01-01'
            end_time: 结束时间，格式：'2024-01-01 23:59:59' 或 '2024-01-01'
            limit: 每页数量，默认200，最大200
            page: 页码，从1开始

        Returns:
            缺陷数据列表
        """
        params = {
            'workspace_id': workspace_id,
            'limit': limit,
            'page': page,
            'order': 'created desc',
        }

        created_filters = []

        if start_time:
            if len(start_time) == 10:
                start_time = f"{start_time} 00:00:00"
            created_filters.append(f"{start_time}")

        if end_time:
            if len(end_time) == 10:
                end_time = f"{end_time} 23:59:59"
            created_filters.append(f"{end_time}")

        if created_filters:
            params['created'] = "~".join(created_filters)

        try:
            response = requests.get(f"{self.base_url}/bugs", headers=self.headers, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


    def get_all_bugs_by_time_range(self, workspace_id, start_time=None, end_time=None, max_bugs=None) -> List[Dict]:
        """
        获取指定时间范围内的所有缺陷（自动分页）

        Args:
            workspace_id: 工作区ID
            start_time: 开始时间，格式：'2024-01-01 00:00:00' 或 '2024-01-01'
            end_time: 结束时间，格式：'2024-01-01 23:59:59' 或 '2024-01-01'
            max_bugs: 最大获取数量，None表示获取所有

        Returns:
            所有缺陷数据列表
        """
        all_bugs = []
        page = 1

        while True:
            # 获取当前页数据
            bugs_page = self.get_bugs_by_time_range(
                workspace_id=workspace_id,
                start_time=start_time,
                end_time=end_time,
                limit=200,
                page=page
            )

            if not bugs_page:
                break

            all_bugs.extend(bugs_page)

            # 检查是否达到最大数量限制
            if max_bugs and len(all_bugs) >= max_bugs:
                all_bugs = all_bugs[:max_bugs]
                break

            # 如果返回的数据少于200条，说明已经是最后一页
            if len(bugs_page) < 200:
                break

            page += 1

        return all_bugs
        
    

    def replace_labels_fields(self, bug_data: dict, labels_fields_config):
        # 生成字段映射字典：{labels -> name}
        # 创建新字典（保留原始非自定义字段，替换已配置的自定义字段）
        new_bug_data = {}
        for key, value in bug_data.items():
            if key in labels_fields_config:
                # 替换自定义字段名称
                new_key = labels_fields_config[key]
            else:
                # 保留非自定义字段
                new_key = key

            # 避免键冲突（如果多个custom_field映射到同一name）
            if new_key in new_bug_data:
                if isinstance(new_bug_data[new_key], list):
                    new_bug_data[new_key].append(value)
                else:
                    if new_bug_data[new_key] == "":
                        new_bug_data[new_key] = value
            else:
                new_bug_data[new_key] = value

        return new_bug_data

    def get_fields_info(self, workspace_id) -> Dict:
        params = {
            'workspace_id': workspace_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/get_fields_info",
                headers=self.headers,
                params=params,
                timeout=10,
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            fields_info = data.get('data', {})
            return fields_info
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e

    def delete_custom_fields(self, new_bug_data):
        # 生成待删除键列表（避免遍历时修改字典结构）
        custom_keys = [k for k in new_bug_data if k.startswith('custom')]

        # 遍历删除（兼容Python3.7+的有序字典特性）
        for key in custom_keys:
            del new_bug_data[key]  # 直接删除不需要返回值的场景[2,6](@ref)
        return new_bug_data
    
    def extract_fields_value(self, bug_data, fields_label):
        new_bug_data = {}
        for key, value in bug_data.items():
            if key in fields_label:
                if "options" in fields_label[key] and isinstance(fields_label[key]["options"], dict):
                    chinese_value = fields_label[key]["options"].get(value, "")
                    new_bug_data[key] = chinese_value
                else:
                    new_bug_data[key] = value
        return new_bug_data
    
    def get_story_info_for_bug(self, workspace_id, bug_data):
        # 获取相关联的需求
        bug_id = bug_data.get("ID", "")
        story_id_list = self.get_bug_related_story(workspace_id, [bug_id])
        # 获取需求名称
        story_name = ""
        story_url = ""
        if story_id_list != [{}]:
            for story_bugs in story_id_list:
                if story_bugs['bug_id'] == bug_id:
                    story_id = story_bugs['story_id']
                    story_name = self.get_story_name(workspace_id, story_id)
                    # 需求链接
                    story_url = f"https://tapd.woa.com/tapd_fe/{str(workspace_id)}/story/detail/{str(story_id)}"
        bug_data["需求链接"] = story_url
        bug_data["需求名称"] = story_name
        return bug_data
    
    def process_bug_field(self, bug_data: dict, workspace_id):
        if bug_data is None or bug_data == {}:
            return {}
        del bug_data["priority"]
        chinese_value_bug_data = self.extract_fields_value(bug_data, self.fields_info)
        # 进行替换
        new_bug_data = self.replace_labels_fields(chinese_value_bug_data, self.fields_label)
        # 删除多余的自定义字段
        new_bug_data_no_custom = self.delete_custom_fields(new_bug_data)
        return new_bug_data_no_custom
    
    def process_bug_data(self, bug_data: dict, workspace_id) -> dict:
        """
        处理BUG数据，根据业务模板将详细描述转化为JSON格式
        """
        new_bug_data_no_custom = self.process_bug_field(bug_data, workspace_id)
        self.get_story_info_for_bug(workspace_id, new_bug_data_no_custom)

        # 获取原始详细描述
        original_description = new_bug_data_no_custom.get("详细描述", "")
        print("原始HTML描述: " + original_description)

        # 使用正则匹配将详细描述转化为JSON格式
        parsed_description = parse_description_to_json(original_description, workspace_id)
        print("解析后的JSON格式: " + json.dumps(parsed_description, ensure_ascii=False, indent=2))

        # 如果成功解析出结构化数据，则使用JSON格式；否则使用原来的MD转换方式
        if parsed_description:
            # 对每个字段分别截取前500字符
            truncated_description = self.truncate_parsed_description_fields(parsed_description)
            print("截取后的JSON格式: " + json.dumps(truncated_description, ensure_ascii=False, indent=2))
            # 将截取后的JSON转换为字符串存储
            new_bug_data_no_custom["详细描述"] = json.dumps(truncated_description, ensure_ascii=False, indent=2)
        else:
            # 回退到原来的HTML转MD方式
            print("未能解析出结构化数据，使用原来的HTML转MD方式")
            bug_html2md(new_bug_data_no_custom, "详细描述")
            print("转换后的MD: " + new_bug_data_no_custom["详细描述"])
            new_bug_data_no_custom["详细描述"] = new_bug_data_no_custom["详细描述"].replace("*", "")
            # 对非结构化数据仍使用原来的长度限制
            new_bug_data_no_custom["详细描述"] = new_bug_data_no_custom["详细描述"][:DESCRIPTION_MAX_LENGTH]

        return new_bug_data_no_custom

    def truncate_parsed_description_fields(self, parsed_description: dict, max_field_length: int = 300) -> dict:
        """
        对解析后的详细描述字典中的每个字段分别截取指定长度

        Args:
            parsed_description: 解析后的详细描述字典
            max_field_length: 每个字段的最大长度，默认500字符

        Returns:
            dict: 截取后的详细描述字典
        """
        truncated_description = {}

        def truncate_value(value, max_length):
            """递归截取值，支持字符串和嵌套字典"""
            if isinstance(value, str):
                return value[:max_length] if len(value) > max_length else value
            elif isinstance(value, dict):
                return {k: truncate_value(v, max_length) for k, v in value.items()}
            else:
                # 对于其他类型（如列表、数字等），转为字符串后截取
                str_value = str(value)
                return str_value[:max_length] if len(str_value) > max_length else str_value

        # 对每个字段进行截取
        for field_name, field_value in parsed_description.items():
            truncated_description[field_name] = truncate_value(field_value, max_field_length)

        return truncated_description

    def get_comments(self, workspace_id, bug_id: list):
        params = {
            'workspace_id': workspace_id,
            'entry_type': 'bug|bug_remark',
            'limit': 100,
            'entry_id': bug_id,
        }
        try:
            response = requests.get(
                f"{self.base_url}/comments",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            return data.get('data', [])
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e    

        # 获取原始bug数据
    def get_bug_all_pure_message(self, workspace_id, bug_id):
        # 获取原始bug数据
        bug_data = self.get_bug_by_id(workspace_id, bug_id)
        if bug_data == {}:
            logger.info("未获取到bug数据")
        return self.process_bug_data(bug_data, workspace_id)

    def get_bug_related_story(self, workspace_id, bug_ids: list) -> list[dict]:
        """
        通过workspace_id和bug_id获取bug想关联的story_id
        """
        params = {
            'workspace_id': workspace_id,
            'bug_id': ",".join(map(str, bug_ids)),        
        }
        try:
            response = requests.get(
                f"{self.base_url}/bugs/get_related_stories",
                headers=self.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()
            if data.get('status') != 1:
                raise TAPDError(f"API返回异常状态码: {data.get('status')}")
            story_id_list: list = data.get('data', [{}])
            return story_id_list 
        except RequestException as e:
            raise TAPDError(f"API请求失败: {str(e)}") from e


# exceptions.py
class TAPDError(Exception):
    """自定义API异常"""
    pass


class HTMLConversionError(Exception):
    """HTML转换异常"""
    pass



@dataclass
class AppConfig:
    """应用配置"""
    auth_token: str = 'UHJvY2Vzc01hbmFnZVBsYXRmcm9tOjFFMjE2NEJCLUUzRkQtQTRBNi1FNUFDLUI1MDMxMDNDQzg3MQ=='
    excluded_tags: List[str] = field(default_factory=lambda: ['script', 'style', 'nav', 'footer'])
    markdown_config: Dict[str, bool] = field(default_factory=lambda: {'body_width': 0, 'wrap_links': True})
    api_timeout: int = 15

config = AppConfig()
tap_client = TAPDClient(auth_token=config.auth_token, workspace_id=BUG_EVALUATION_WORKSPACE_ID)
if __name__ == '__main__':
    test = "update_bug"
    if test == "get_bug_by_id":
        print(json.dumps(tap_client.get_bug_by_id(workspace_id=20375472, bug_id=1020375472142465367), indent=4, ensure_ascii=False))
    elif test == "get_image":
        print(tap_client.get_image(20375472, "/tfl/captures/2025-05/tapd_20375472_base64_1747364325_203.png"))
    elif test == "get_bug_related_story":
        print(tap_client.get_bug_related_story(20375472, [1020375472142465367]))
    elif test == "get_custom_fields_settings":
        print(tap_client.get_custom_fields_settings(workspace_id=20375472))
    elif test == "get_bug_all_pure_message":
        print(tap_client.get_bug_all_pure_message(workspace_id=20375472, bug_id=1020375472142465367))
    elif test == "get_stories_by_ids":
        print(tap_client.get_stories_by_ids(workspace_id=20375472, ids=[1020375472124209140, 1020375472124206566]))
    elif test == "get_comments":
        print(tap_client.get_comments(workspace_id=20375472, bug_id=[1020375472142176342, 1020375472142052015]))
    elif test == "update_bug":
        import re
        import markdown
        def convert_custom_video(md_text: str) -> str:
            # 把 !video[alt](url) 替换成 <video controls src="url">alt</video>
            video_pattern = re.compile(r'!video\[(.*?)\]\((.*?)\)')
            md_text = video_pattern.sub(r'<video controls src="\2">\1</video>', md_text)
            return md_text

        md_text = '''
**【条件】**  
体验版  
**【机型】**  
华为mate60，操作系统版本未提供

**【步骤】**  
1. 打开App->进入首页->点击城市切换按钮->选择不同城市  
2. 观察每次切换城市时的弹窗行为  
3. 记录弹窗出现的频率和内容

**【预期结果】**  
切换城市时不应频繁弹出运营弹窗，或弹窗出现频率应符合设计规范

**【实际结果】**  
每次切城市都会弹出运营弹窗，影响用户体验

!video[video](https://tapd.woa.com/20375472/attachments/preview_attachments/1020375472536206842/bug_description)
'''

        processed_md = convert_custom_video(md_text)
        html = markdown.markdown(processed_md)
        print(html)
        print(tap_client.update_bug(workspace_id=20375472, bug_id=1020375472143567123, description=html))