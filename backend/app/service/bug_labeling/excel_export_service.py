"""
Excel导出服务
用于将BUG打标结果导出到Excel文件
"""
import os
import pandas as pd
from datetime import datetime
from typing import List, Dict, Optional
from pathlib import Path
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from backend.app.utils.logger_util import logger
from backend.app.service.bug_labeling.labeling_service import LabelingResult


class ExcelExportService:
    """Excel导出服务"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化导出服务
        
        Args:
            output_dir: 输出目录，默认为项目根目录下的exports文件夹
        """
        if output_dir is None:
            project_root = Path(__file__).parent.parent.parent.parent.parent
            output_dir = project_root / "exports"
        
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def export_labeling_results(
        self, 
        results: List[Dict], 
        filename: str = None,
        include_raw_data: bool = True
    ) -> str:
        """
        导出打标结果到Excel
        
        Args:
            results: 打标结果列表，每个元素包含bug_data和labeling_result
            filename: 文件名，如果不指定则自动生成
            include_raw_data: 是否包含原始BUG数据
            
        Returns:
            str: 导出文件的完整路径
        """
        try:
            if not results:
                raise ValueError("没有数据可导出")
            
            # 生成文件名
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"bug_labeling_results_{timestamp}.xlsx"
            
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            
            filepath = self.output_dir / filename
            
            # 准备数据
            export_data = self._prepare_export_data(results, include_raw_data)
            
            # 创建Excel文件
            self._create_excel_file(export_data, filepath)
            
            logger.info(f"成功导出 {len(results)} 条打标结果到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"导出Excel失败: {str(e)}")
            raise e
    
    def _prepare_export_data(self, results: List[Dict], include_raw_data: bool) -> List[Dict]:
        """准备导出数据"""
        export_data = []
        
        for i, result in enumerate(results, 1):
            bug_data = result.get('bug_data', {})
            labeling_result = result.get('labeling_result', {})
            
            # 基础数据
            row_data = {
                '序号': i,
                'BUG_ID': bug_data.get('ID', ''),
                '标题': bug_data.get('标题', ''),
                '状态': bug_data.get('状态', ''),
                '创建人': bug_data.get('创建人', ''),
                '模块': bug_data.get('模块', ''),
                '优先级': bug_data.get('优先级', ''),
                '严重程度': bug_data.get('严重程度', ''),
                '创建时间': bug_data.get('创建时间', ''),
            }
            
            # 打标结果
            if isinstance(labeling_result, dict):
                row_data.update({
                    '问题类型': labeling_result.get('problem_type', ''),
                    '具体功能': labeling_result.get('specific_function', ''),
                    '置信度': labeling_result.get('confidence', 0.0),
                    '是否新问题类型': '是' if labeling_result.get('is_new_problem_type', False) else '否',
                    '是否新具体功能': '是' if labeling_result.get('is_new_specific_function', False) else '否',
                    '推理过程': labeling_result.get('reasoning', ''),
                    '错误信息': labeling_result.get('error_message', ''),
                })
            else:
                # 如果labeling_result是LabelingResult对象
                row_data.update({
                    '问题类型': getattr(labeling_result, 'problem_type', ''),
                    '具体功能': getattr(labeling_result, 'specific_function', ''),
                    '置信度': getattr(labeling_result, 'confidence', 0.0),
                    '是否新问题类型': '是' if getattr(labeling_result, 'is_new_problem_type', False) else '否',
                    '是否新具体功能': '是' if getattr(labeling_result, 'is_new_specific_function', False) else '否',
                    '推理过程': getattr(labeling_result, 'reasoning', ''),
                    '错误信息': getattr(labeling_result, 'error_message', ''),
                })
            
            # 包含原始数据
            if include_raw_data:
                row_data['详细描述'] = str(bug_data.get('详细描述', ''))[:1000]  # 限制长度
                row_data['需求链接'] = bug_data.get('需求链接', '')
                row_data['需求名称'] = bug_data.get('需求名称', '')
            
            export_data.append(row_data)
        
        return export_data
    
    def _create_excel_file(self, data: List[Dict], filepath: Path):
        """创建Excel文件"""
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "BUG打标结果"
        
        # 写入数据
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
        
        # 设置样式
        self._apply_excel_styles(ws, len(data))
        
        # 保存文件
        wb.save(filepath)
    
    def _apply_excel_styles(self, worksheet, data_rows: int):
        """应用Excel样式"""
        # 标题行样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        # 边框样式
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 应用标题行样式
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border
        
        # 应用数据行样式
        for row in range(2, data_rows + 2):
            for col in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row=row, column=col)
                cell.border = thin_border
                cell.alignment = Alignment(vertical="center", wrap_text=True)
        
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            # 设置列宽，最小10，最大50
            adjusted_width = min(max(max_length + 2, 10), 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def export_summary_report(self, results: List[Dict], filename: str = None) -> str:
        """
        导出汇总报告
        
        Args:
            results: 打标结果列表
            filename: 文件名
            
        Returns:
            str: 导出文件路径
        """
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"bug_labeling_summary_{timestamp}.xlsx"
            
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            
            filepath = self.output_dir / filename
            
            # 创建工作簿
            wb = Workbook()
            
            # 创建汇总统计表
            self._create_summary_sheet(wb, results)
            
            # 创建问题类型统计表
            self._create_problem_type_sheet(wb, results)
            
            # 创建具体功能统计表
            self._create_function_sheet(wb, results)
            
            # 删除默认工作表
            if 'Sheet' in wb.sheetnames:
                wb.remove(wb['Sheet'])
            
            # 保存文件
            wb.save(filepath)
            
            logger.info(f"成功导出汇总报告到: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"导出汇总报告失败: {str(e)}")
            raise e
    
    def _create_summary_sheet(self, workbook, results: List[Dict]):
        """创建汇总统计工作表"""
        ws = workbook.active
        ws.title = "汇总统计"
        
        # 统计数据
        total_count = len(results)
        success_count = sum(1 for r in results if not r.get('labeling_result', {}).get('error_message'))
        error_count = total_count - success_count
        new_problem_types = sum(1 for r in results if r.get('labeling_result', {}).get('is_new_problem_type'))
        new_functions = sum(1 for r in results if r.get('labeling_result', {}).get('is_new_specific_function'))
        
        # 写入统计数据
        summary_data = [
            ['统计项目', '数量'],
            ['总BUG数量', total_count],
            ['成功打标数量', success_count],
            ['失败数量', error_count],
            ['新增问题类型数量', new_problem_types],
            ['新增具体功能数量', new_functions],
            ['成功率', f"{success_count/total_count*100:.1f}%" if total_count > 0 else "0%"]
        ]
        
        for row_data in summary_data:
            ws.append(row_data)
        
        # 应用样式
        self._apply_summary_styles(ws)
    
    def _create_problem_type_sheet(self, workbook, results: List[Dict]):
        """创建问题类型统计工作表"""
        ws = workbook.create_sheet("问题类型统计")
        
        # 统计问题类型
        type_count = {}
        for result in results:
            problem_type = result.get('labeling_result', {}).get('problem_type', '未分类')
            type_count[problem_type] = type_count.get(problem_type, 0) + 1
        
        # 写入数据
        ws.append(['问题类型', '数量', '占比'])
        total = len(results)
        for problem_type, count in sorted(type_count.items(), key=lambda x: x[1], reverse=True):
            percentage = f"{count/total*100:.1f}%" if total > 0 else "0%"
            ws.append([problem_type, count, percentage])
        
        self._apply_summary_styles(ws)
    
    def _create_function_sheet(self, workbook, results: List[Dict]):
        """创建具体功能统计工作表"""
        ws = workbook.create_sheet("具体功能统计")
        
        # 统计具体功能
        function_count = {}
        for result in results:
            specific_function = result.get('labeling_result', {}).get('specific_function', '未分类')
            function_count[specific_function] = function_count.get(specific_function, 0) + 1
        
        # 写入数据
        ws.append(['具体功能', '数量', '占比'])
        total = len(results)
        for function, count in sorted(function_count.items(), key=lambda x: x[1], reverse=True):
            percentage = f"{count/total*100:.1f}%" if total > 0 else "0%"
            ws.append([function, count, percentage])
        
        self._apply_summary_styles(ws)
    
    def _apply_summary_styles(self, worksheet):
        """应用汇总表样式"""
        # 标题行样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center", vertical="center")
        
        # 自动调整列宽
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max(max_length + 2, 15), 40)
            worksheet.column_dimensions[column_letter].width = adjusted_width


# 创建全局导出服务实例
excel_export_service = ExcelExportService()


if __name__ == "__main__":
    # 测试代码
    service = ExcelExportService()
    
    # 模拟测试数据
    test_results = [
        {
            'bug_data': {
                'ID': '123456',
                '标题': '测试BUG标题',
                '状态': '新建',
                '创建人': '测试用户',
                '模块': '健康问问'
            },
            'labeling_result': {
                'problem_type': '交互逻辑问题/操作冲突',
                'specific_function': '智能导诊/性别年龄选择',
                'confidence': 0.85,
                'is_new_problem_type': False,
                'is_new_specific_function': False,
                'reasoning': '测试推理过程'
            }
        }
    ]
    
    # 测试导出
    try:
        filepath = service.export_labeling_results(test_results, "test_export.xlsx")
        print(f"测试导出成功: {filepath}")
        
        summary_path = service.export_summary_report(test_results, "test_summary.xlsx")
        print(f"汇总报告导出成功: {summary_path}")
    except Exception as e:
        print(f"测试失败: {e}")
