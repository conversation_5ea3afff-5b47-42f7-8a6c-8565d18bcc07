"""
缺陷自动打标服务
基于大模型实现缺陷的自动标签分类
"""
import json
import re
from typing import Dict, Optional
from openai import OpenAI
from backend.app.config.config import HUNYUAN_API_URL, HUNYUAN_API_KEY, HUNYUAN_MODEL
from backend.app.utils.logger_util import logger
from backend.app.service.bug_labeling.label_manager import label_manager
from pydantic import BaseModel


class LabelingResult(BaseModel):
    """打标结果模型"""
    problem_type: str = ""  # 问题类型
    specific_function: str = ""  # 具体功能
    confidence: float = 0.0  # 置信度
    is_new_problem_type: bool = False  # 是否为新创建的问题类型
    is_new_specific_function: bool = False  # 是否为新创建的具体功能
    reasoning: str = ""  # 推理过程
    error_message: str = ""  # 错误信息


class BugLabelingService:
    """缺陷打标服务"""
    
    def __init__(self):
        """初始化打标服务"""
        self.client = OpenAI(
            base_url=HUNYUAN_API_URL,
            api_key=HUNYUAN_API_KEY,
        )
        self.model = HUNYUAN_MODEL
        self.max_retries = 3
        self.retry_interval = 2
    
    def _build_labeling_prompt(self, bug_data: Dict) -> str:
        """构建打标提示词"""
        # 获取当前标签库
        current_labels = label_manager.get_all_labels()
        
        prompt = f"""# 缺陷自动打标任务

## 背景
你是一个专业的缺陷分析专家，需要根据缺陷信息为其打上合适的标签。

## 任务目标
根据提供的缺陷信息，为其分配"问题类型"和"具体"两个维度的标签。
具体功能的核心原则：具体功能标签必须指向 BUG 出问题的对象，而不是场景、路径或操作步骤。
## 当前标签库
### 问题类型标签：
{json.dumps(current_labels.get('问题类型', []), ensure_ascii=False, indent=2)}

### 具体功能标签：
{json.dumps(current_labels.get('具体功能', []), ensure_ascii=False, indent=2)}

## 缺陷信息
```json
{json.dumps(bug_data, ensure_ascii=False, indent=2)}
```

## 标签选择规则     
1. **优先复用现有标签**：首先尝试从现有标签库中选择最合适的标签
2. **创建新标签的条件**：
   - 现有标签都不能准确描述该缺陷时
   - 新标签应该具有通用性，能够适用于类似的缺陷
   - 新标签命名应该清晰、简洁、易理解
   - 具体功能应为大功能/小功能，大功能为bug对应模块下的细分功能，小功能为大功能下的细分功能，若小功能无法细分，则可忽略小功能。
## 注意事项
1. op配置平台为具体业务的配置，因此具体功能应为具体的业务功能，而非运营配置。

## 输出要求
请严格按照以下JSON格式输出结果：

```json
{{
  "problem_type": "选择的问题类型标签",
  "specific_function": "选择的具体功能标签", 
  "confidence": 0.85,
  "is_new_problem_type": false,
  "is_new_specific_function": false,
  "reasoning": "详细的推理过程，说明为什么选择这些标签"
}}
```

## 注意事项
1. confidence字段表示置信度，范围0-1
2. 如果创建了新标签，请将对应的is_new_*字段设为true
3. reasoning字段要详细说明选择标签的理由
4. 必须严格按照JSON格式输出，不要包含其他内容
"""
        return prompt
    
    async def label_bug(self, bug_data: Dict) -> LabelingResult:
        """
        为缺陷打标
        
        Args:
            bug_data: 缺陷数据
            
        Returns:
            LabelingResult: 打标结果
        """
        if not HUNYUAN_API_KEY:
            logger.error("未配置混元API密钥，无法进行打标")
            return LabelingResult(error_message="未配置混元API密钥")
        
        try:
            # 构建提示词
            prompt = self._build_labeling_prompt(bug_data)
            logger.info(f"开始为缺陷打标: {bug_data.get('标题', '未知标题')}")
            
            # 调用大模型
            for attempt in range(self.max_retries):
                try:
                    response = self.client.chat.completions.create(
                        model=self.model,
                        messages=[
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        temperature=0.1,  # 降低随机性，提高一致性
                        stream=False
                    )
                    
                    content = response.choices[0].message.content.strip()
                    logger.info(f"大模型原始响应: {content}")
                    
                    # 解析响应
                    result = self._parse_response(content)
                    
                    # 更新标签库
                    if result.is_new_problem_type and result.problem_type:
                        label_manager.add_problem_type(result.problem_type)
                    
                    if result.is_new_specific_function and result.specific_function:
                        label_manager.add_specific_function(result.specific_function)
                    
                    logger.info(f"打标完成: 问题类型={result.problem_type}, 具体功能={result.specific_function}")
                    return result
                    
                except Exception as e:
                    logger.error(f"调用大模型失败 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                    if attempt < self.max_retries - 1:
                        import asyncio
                        await asyncio.sleep(self.retry_interval)
                    else:
                        raise e
                        
        except Exception as e:
            logger.error(f"缺陷打标失败: {str(e)}")
            return LabelingResult(error_message=f"打标失败: {str(e)}")
    
    def _parse_response(self, content: str) -> LabelingResult:
        """解析大模型响应"""
        try:
            # 清理响应内容
            content = self._clean_json_response(content)
            
            # 解析JSON
            data = json.loads(content)
            
            return LabelingResult(
                problem_type=data.get("problem_type", ""),
                specific_function=data.get("specific_function", ""),
                confidence=float(data.get("confidence", 0.0)),
                is_new_problem_type=bool(data.get("is_new_problem_type", False)),
                is_new_specific_function=bool(data.get("is_new_specific_function", False)),
                reasoning=data.get("reasoning", "")
            )
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}, 原始内容: {content}")
            return LabelingResult(error_message=f"响应解析失败: {str(e)}")
        except Exception as e:
            logger.error(f"响应处理失败: {str(e)}")
            return LabelingResult(error_message=f"响应处理失败: {str(e)}")
    
    def _clean_json_response(self, content: str) -> str:
        """清理JSON响应内容"""
        # 移除代码块标记
        content = re.sub(r'^```json\s*|\s*```$', '', content, flags=re.MULTILINE)
        content = re.sub(r'^```\s*|\s*```$', '', content, flags=re.MULTILINE)
        
        # 查找JSON内容
        content = content.strip()
        
        # 找到第一个{和最后一个}
        start_idx = content.find('{')
        end_idx = content.rfind('}')
        
        if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
            content = content[start_idx:end_idx + 1]
        
        return content


# 创建全局打标服务实例
labeling_service = BugLabelingService()


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_labeling():
        service = BugLabelingService()
        
        # 测试数据
        test_bug = {
            "标题": "智能导诊页面性别选择按钮点击无反应",
            "详细描述": "在智能导诊页面，点击性别选择按钮时没有任何反应，无法进行下一步操作",
            "模块": "健康问问",
            "状态": "新建"
        }
        
        result = await service.label_bug(test_bug)
        print(f"打标结果: {result}")
    
    # 运行测试
    # asyncio.run(test_labeling())
