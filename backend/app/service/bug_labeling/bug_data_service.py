"""
BUG数据获取和过滤服务
专门用于获取健康问问模块且拒绝原因为空的BUG数据
"""
import json
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from backend.app.utils.tapd import tap_client
from backend.app.config.config import JIANKANG_WORKSPACE_ID
from backend.app.utils.logger_util import logger


class BugDataService:
    """BUG数据获取和过滤服务"""
    
    def __init__(self):
        """初始化服务"""
        self.workspace_id = JIANKANG_WORKSPACE_ID
        self.health_module_keywords = [
            "健康问问", "智能导诊", "问诊", "导诊", "健康咨询", 
            "症状", "疾病", "医疗", "健康", "问答"
        ]
    
    def get_bugs_for_labeling(
        self,
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """
        获取用于打标的BUG数据

        Args:
            start_time: 开始时间，格式：'2024-01-01' 或 '2024-01-01 00:00:00'
            end_time: 结束时间，格式：'2024-01-01' 或 '2024-01-01 23:59:59'
            limit: 最大获取数量

        Returns:
            List[Dict]: 符合条件的BUG数据列表
        """
        try:
            logger.info(f"开始获取健康问问模块的BUG数据，时间范围: {start_time} - {end_time}")

            # 如果没有指定时间范围，默认获取最近7天的数据
            if not start_time:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
                start_time = start_date.strftime("%Y-%m-%d")
                end_time = end_date.strftime("%Y-%m-%d")

            # 直接通过API参数过滤BUG数据
            filtered_bugs = self._get_filtered_bugs_from_api(
                start_time=start_time,
                end_time=end_time,
                limit=limit
            )

            logger.info(f"从TAPD获取到 {len(filtered_bugs)} 个符合条件的BUG")

            # 处理BUG数据
            processed_bugs = []
            for bug_item in filtered_bugs:
                try:
                    raw_bug = bug_item.get("Bug", {})
                    processed_bug = tap_client.process_bug_data(raw_bug, self.workspace_id)
                    if processed_bug:
                        processed_bugs.append(processed_bug)

                except Exception as e:
                    logger.error(f"处理BUG数据失败: {str(e)}")
                    continue

            logger.info(f"成功处理 {len(processed_bugs)} 个BUG")
            return processed_bugs

        except Exception as e:
            logger.error(f"获取BUG数据失败: {str(e)}")
            return []
    
    def _get_filtered_bugs_from_api(
        self,
        start_time: str,
        end_time: str,
        limit: int
    ) -> List[Dict]:
        """
        通过API参数直接过滤BUG数据

        Args:
            start_time: 开始时间
            end_time: 结束时间
            limit: 限制数量

        Returns:
            List[Dict]: 过滤后的BUG数据
        """
        try:
            # 构建API请求参数
            params = {
                'workspace_id': self.workspace_id,
                'limit': min(limit * 2, 200),  # 适当增加获取数量，考虑到可能有无效数据
                'page': 1,
                'order': 'created desc',
            }

            # 添加时间范围过滤
            created_filters = []
            if start_time:
                if len(start_time) == 10:
                    start_time = f"{start_time} 00:00:00"
                created_filters.append(start_time)

            if end_time:
                if len(end_time) == 10:
                    end_time = f"{end_time} 23:59:59"
                created_filters.append(end_time)

            if created_filters:
                params['created'] = "~".join(created_filters)

            # 添加模块过滤 - 健康问问相关模块
            # 注意：这里需要根据实际的TAPD模块名称进行调整
            health_modules = ["健康问问", "智能导诊", "问诊", "导诊"]
            # 如果TAPD支持模块过滤，可以添加：
            # params['module'] = "|".join(health_modules)

            # 添加拒绝时间过滤 - 只获取拒绝时间为空的BUG
            # 注意：这里需要根据实际的TAPD字段名称进行调整
            # params['reject_time'] = ""  # 或者使用其他方式表示空值

            # 排除已关闭状态的BUG
            excluded_status = ["已关闭", "已拒绝", "已取消", "Closed", "Rejected"]
            # 如果TAPD支持状态排除，可以添加：
            # params['status'] = f"!{','.join(excluded_status)}"

            logger.info(f"API请求参数: {params}")

            # 调用TAPD API获取BUG数据
            bugs = self._call_tapd_bugs_api(params)

            # 进行二次过滤，确保数据质量
            filtered_bugs = []
            for bug_item in bugs:
                bug_data = bug_item.get("Bug", {})

                # 检查是否为健康问问相关（如果API不支持模块过滤）
                if not self._is_health_related_simple(bug_data):
                    continue

                # 检查拒绝原因是否为空（如果API不支持reject_time过滤）
                if self._has_reject_reason(bug_data):
                    continue

                # 检查基本数据完整性
                if not self._is_valid_bug_data(bug_data):
                    continue

                filtered_bugs.append(bug_item)

                # 达到限制数量就停止
                if len(filtered_bugs) >= limit:
                    break

            return filtered_bugs

        except Exception as e:
            logger.error(f"API过滤获取BUG失败: {str(e)}")
            # 如果API过滤失败，回退到原来的方法
            return tap_client.get_all_bugs_by_time_range(
                workspace_id=self.workspace_id,
                start_time=start_time,
                end_time=end_time,
                max_bugs=limit
            )

    def _is_health_related_simple(self, bug_data: Dict) -> bool:
        """简单的健康问问相关性检查"""
        module = bug_data.get("module", "")
        title = bug_data.get("title", "")

        # 合并文本检查
        combined_text = f"{module} {title}".lower()

        # 检查关键词
        for keyword in self.health_module_keywords:
            if keyword.lower() in combined_text:
                return True

        return False

    def _has_reject_reason(self, bug_data: Dict) -> bool:
        """检查是否有拒绝原因"""
        reject_reason = bug_data.get("reject_reason", "") or bug_data.get("拒绝原因", "")
        reject_time = bug_data.get("reject_time", "") or bug_data.get("拒绝时间", "")

        return bool(reject_reason and reject_reason.strip()) or bool(reject_time and reject_time.strip())

    def _is_valid_bug_data(self, bug_data: Dict) -> bool:
        """检查BUG数据是否有效"""
        title = bug_data.get("title", "")
        status = bug_data.get("status", "")

        # 检查标题
        if not title or not title.strip():
            return False

        # 检查状态
        excluded_status = ["已关闭", "已拒绝", "已取消", "Closed", "Rejected"]
        if status in excluded_status:
            return False

        return True
    
    def get_bug_by_id(self, bug_id: str) -> Optional[Dict]:
        """
        根据ID获取单个BUG数据

        Args:
            bug_id: BUG ID

        Returns:
            Optional[Dict]: BUG数据，如果不存在或不符合条件则返回None
        """
        try:
            logger.info(f"获取BUG数据: {bug_id}")

            # 获取原始BUG数据
            raw_bug = tap_client.get_bug_by_id(self.workspace_id, bug_id)
            if not raw_bug:
                logger.warning(f"未找到BUG: {bug_id}")
                return None

            # 检查是否符合条件
            if not self._is_health_related_simple(raw_bug):
                logger.info(f"BUG {bug_id} 不是健康问问相关模块")
                return None

            if self._has_reject_reason(raw_bug):
                logger.info(f"BUG {bug_id} 有拒绝原因，不符合打标条件")
                return None

            if not self._is_valid_bug_data(raw_bug):
                logger.info(f"BUG {bug_id} 数据无效，不符合打标条件")
                return None

            # 处理BUG数据
            processed_bug = tap_client.process_bug_data(raw_bug, self.workspace_id)
            return processed_bug

        except Exception as e:
            logger.error(f"获取BUG数据失败: {str(e)}")
            return None
    
    def get_bugs_by_ids(self, bug_ids: List[str]) -> List[Dict]:
        """
        根据ID列表批量获取BUG数据
        
        Args:
            bug_ids: BUG ID列表
            
        Returns:
            List[Dict]: 符合条件的BUG数据列表
        """
        bugs = []
        for bug_id in bug_ids:
            bug_data = self.get_bug_by_id(bug_id)
            if bug_data:
                bugs.append(bug_data)
        
        logger.info(f"批量获取BUG数据完成，输入 {len(bug_ids)} 个ID，返回 {len(bugs)} 个有效BUG")
        return bugs
    
    def get_recent_bugs(self, days: int = 7, limit: int = 100) -> List[Dict]:
        """
        获取最近几天的BUG数据
        
        Args:
            days: 天数
            limit: 最大数量
            
        Returns:
            List[Dict]: BUG数据列表
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        return self.get_bugs_for_labeling(
            start_time=start_date.strftime("%Y-%m-%d"),
            end_time=end_date.strftime("%Y-%m-%d"),
            limit=limit
        )
    
    def get_statistics(self, start_time: str = None, end_time: str = None) -> Dict:
        """
        获取BUG数据统计信息
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            Dict: 统计信息
        """
        try:
            bugs = self.get_bugs_for_labeling(start_time, end_time, limit=1000)
            
            # 统计各种信息
            total_count = len(bugs)
            status_count = {}
            module_count = {}
            
            for bug in bugs:
                status = bug.get("状态", "未知")
                module = bug.get("模块", "未知")
                
                status_count[status] = status_count.get(status, 0) + 1
                module_count[module] = module_count.get(module, 0) + 1
            
            return {
                "总数量": total_count,
                "状态分布": status_count,
                "模块分布": module_count,
                "时间范围": f"{start_time} - {end_time}"
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}

    def _call_tapd_bugs_api(self, params: Dict) -> List[Dict]:
        """
        调用TAPD API获取BUG数据

        Args:
            params: API请求参数

        Returns:
            List[Dict]: BUG数据列表
        """
        try:
            import requests
            response = requests.get(
                f"{tap_client.base_url}/bugs",
                headers=tap_client.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()

            if data.get('status') != 1:
                logger.error(f"API返回异常状态码: {data.get('status')}")
                return []

            return data.get('data', [])

        except Exception as e:
            logger.error(f"调用TAPD API失败: {str(e)}")
            return []


# 创建全局服务实例
bug_data_service = BugDataService()


if __name__ == "__main__":
    # 测试代码
    service = BugDataService()
    
    # 获取最近3天的BUG数据
    bugs = service.get_recent_bugs(days=3, limit=10)
    print(f"获取到 {len(bugs)} 个BUG")
    
    # 打印前几个BUG的基本信息
    for i, bug in enumerate(bugs[:3]):
        print(f"\nBUG {i+1}:")
        print(f"  ID: {bug.get('ID', '')}")
        print(f"  标题: {bug.get('标题', '')}")
        print(f"  模块: {bug.get('模块', '')}")
        print(f"  状态: {bug.get('状态', '')}")
    
    # 获取统计信息
    stats = service.get_statistics()
    print(f"\n统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
