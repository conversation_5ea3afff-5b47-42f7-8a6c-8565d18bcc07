"""
BUG数据获取和过滤服务
专门用于获取健康问问模块且拒绝原因为空的BUG数据
"""
import json
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from backend.app.utils.tapd import tap_client
from backend.app.config.config import JIANKANG_WORKSPACE_ID
from backend.app.utils.logger_util import logger


class BugDataService:
    """BUG数据获取和过滤服务"""
    
    def __init__(self):
        """初始化服务"""
        self.workspace_id = JIANKANG_WORKSPACE_ID
        self.health_module_keywords = [
            "健康问问", "智能导诊", "问诊", "导诊", "健康咨询", 
            "症状", "疾病", "医疗", "健康", "问答"
        ]
    
    def get_bugs_for_labeling(
        self, 
        start_time: Optional[str] = None,
        end_time: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict]:
        """
        获取用于打标的BUG数据
        
        Args:
            start_time: 开始时间，格式：'2024-01-01' 或 '2024-01-01 00:00:00'
            end_time: 结束时间，格式：'2024-01-01' 或 '2024-01-01 23:59:59'
            limit: 最大获取数量
            
        Returns:
            List[Dict]: 符合条件的BUG数据列表
        """
        try:
            logger.info(f"开始获取健康问问模块的BUG数据，时间范围: {start_time} - {end_time}")
            
            # 如果没有指定时间范围，默认获取最近7天的数据
            if not start_time:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)
                start_time = start_date.strftime("%Y-%m-%d")
                end_time = end_date.strftime("%Y-%m-%d")
            
            # 获取指定时间范围的所有BUG
            all_bugs = tap_client.get_all_bugs_by_time_range(
                workspace_id=self.workspace_id,
                start_time=start_time,
                end_time=end_time,
                max_bugs=limit * 3  # 获取更多数据用于过滤
            )
            
            logger.info(f"从TAPD获取到 {len(all_bugs)} 个BUG")
            
            # 过滤符合条件的BUG
            filtered_bugs = []
            for bug_item in all_bugs:
                try:
                    raw_bug = bug_item.get("Bug", {})
                    if self._should_include_bug(raw_bug):
                        # 处理BUG数据
                        processed_bug = tap_client.process_bug_data(raw_bug, self.workspace_id)
                        if processed_bug:
                            filtered_bugs.append(processed_bug)
                            
                        # 达到限制数量就停止
                        if len(filtered_bugs) >= limit:
                            break
                            
                except Exception as e:
                    logger.error(f"处理BUG数据失败: {str(e)}")
                    continue
            
            logger.info(f"过滤后得到 {len(filtered_bugs)} 个符合条件的BUG")
            return filtered_bugs
            
        except Exception as e:
            logger.error(f"获取BUG数据失败: {str(e)}")
            return []
    
    def _should_include_bug(self, bug_data: Dict) -> bool:
        """
        判断BUG是否应该包含在打标数据中
        
        Args:
            bug_data: 原始BUG数据
            
        Returns:
            bool: 是否应该包含
        """
        try:
            # 检查模块是否为健康问问相关
            module = bug_data.get("module", "")
            title = bug_data.get("title", "")
            description = bug_data.get("description", "")
            
            # 检查是否为健康问问模块
            is_health_module = self._is_health_related(module, title, description)
            if not is_health_module:
                return False
            
            # 检查拒绝原因是否为空（TAPD中的字段名可能不同）
            reject_reason = bug_data.get("reject_reason", "") or bug_data.get("拒绝原因", "")
            if reject_reason and reject_reason.strip():
                logger.debug(f"BUG {bug_data.get('id', '')} 有拒绝原因，跳过: {reject_reason}")
                return False
            
            # 检查状态，排除已关闭的BUG
            status = bug_data.get("status", "")
            if status in ["已关闭", "已拒绝", "已取消"]:
                logger.debug(f"BUG {bug_data.get('id', '')} 状态为 {status}，跳过")
                return False
            
            # 检查标题和描述是否有效
            if not title or not title.strip():
                logger.debug(f"BUG {bug_data.get('id', '')} 标题为空，跳过")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"判断BUG是否包含失败: {str(e)}")
            return False
    
    def _is_health_related(self, module: str, title: str, description: str) -> bool:
        """
        判断BUG是否与健康问问相关
        
        Args:
            module: 模块名称
            title: 标题
            description: 描述
            
        Returns:
            bool: 是否相关
        """
        # 合并所有文本进行检查
        combined_text = f"{module} {title} {description}".lower()
        
        # 检查是否包含健康问问相关关键词
        for keyword in self.health_module_keywords:
            if keyword.lower() in combined_text:
                return True
        
        return False
    
    def get_bug_by_id(self, bug_id: str) -> Optional[Dict]:
        """
        根据ID获取单个BUG数据
        
        Args:
            bug_id: BUG ID
            
        Returns:
            Optional[Dict]: BUG数据，如果不存在或不符合条件则返回None
        """
        try:
            logger.info(f"获取BUG数据: {bug_id}")
            
            # 获取原始BUG数据
            raw_bug = tap_client.get_bug_by_id(self.workspace_id, bug_id)
            if not raw_bug:
                logger.warning(f"未找到BUG: {bug_id}")
                return None
            
            # 检查是否符合条件
            if not self._should_include_bug(raw_bug):
                logger.info(f"BUG {bug_id} 不符合打标条件")
                return None
            
            # 处理BUG数据
            processed_bug = tap_client.process_bug_data(raw_bug, self.workspace_id)
            return processed_bug
            
        except Exception as e:
            logger.error(f"获取BUG数据失败: {str(e)}")
            return None
    
    def get_bugs_by_ids(self, bug_ids: List[str]) -> List[Dict]:
        """
        根据ID列表批量获取BUG数据
        
        Args:
            bug_ids: BUG ID列表
            
        Returns:
            List[Dict]: 符合条件的BUG数据列表
        """
        bugs = []
        for bug_id in bug_ids:
            bug_data = self.get_bug_by_id(bug_id)
            if bug_data:
                bugs.append(bug_data)
        
        logger.info(f"批量获取BUG数据完成，输入 {len(bug_ids)} 个ID，返回 {len(bugs)} 个有效BUG")
        return bugs
    
    def get_recent_bugs(self, days: int = 7, limit: int = 100) -> List[Dict]:
        """
        获取最近几天的BUG数据
        
        Args:
            days: 天数
            limit: 最大数量
            
        Returns:
            List[Dict]: BUG数据列表
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        return self.get_bugs_for_labeling(
            start_time=start_date.strftime("%Y-%m-%d"),
            end_time=end_date.strftime("%Y-%m-%d"),
            limit=limit
        )
    
    def get_statistics(self, start_time: str = None, end_time: str = None) -> Dict:
        """
        获取BUG数据统计信息
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            Dict: 统计信息
        """
        try:
            bugs = self.get_bugs_for_labeling(start_time, end_time, limit=1000)
            
            # 统计各种信息
            total_count = len(bugs)
            status_count = {}
            module_count = {}
            
            for bug in bugs:
                status = bug.get("状态", "未知")
                module = bug.get("模块", "未知")
                
                status_count[status] = status_count.get(status, 0) + 1
                module_count[module] = module_count.get(module, 0) + 1
            
            return {
                "总数量": total_count,
                "状态分布": status_count,
                "模块分布": module_count,
                "时间范围": f"{start_time} - {end_time}"
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}


# 创建全局服务实例
bug_data_service = BugDataService()


if __name__ == "__main__":
    # 测试代码
    service = BugDataService()
    
    # 获取最近3天的BUG数据
    bugs = service.get_recent_bugs(days=3, limit=10)
    print(f"获取到 {len(bugs)} 个BUG")
    
    # 打印前几个BUG的基本信息
    for i, bug in enumerate(bugs[:3]):
        print(f"\nBUG {i+1}:")
        print(f"  ID: {bug.get('ID', '')}")
        print(f"  标题: {bug.get('标题', '')}")
        print(f"  模块: {bug.get('模块', '')}")
        print(f"  状态: {bug.get('状态', '')}")
    
    # 获取统计信息
    stats = service.get_statistics()
    print(f"\n统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
