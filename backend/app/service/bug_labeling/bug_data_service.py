"""
BUG数据获取和过滤服务
专门用于获取健康问问模块且拒绝原因为空的BUG数据
"""
import json
from typing import List, Dict, Optional
from backend.app.utils.tapd import tap_client
from backend.app.config.config import JIANKANG_WORKSPACE_ID
from backend.app.utils.logger_util import logger


class BugDataService:
    """BUG数据获取和过滤服务"""

    def __init__(self):
        """初始化服务"""
        self.workspace_id = JIANKANG_WORKSPACE_ID
    
    def get_bugs_for_labeling(self, limit: int = 100):
        """
        获取用于打标的BUG数据，逐个处理并返回生成器

        Args:
            limit: 最大获取数量

        Yields:
            Dict: 处理后的BUG数据
        """
        try:
            logger.info("开始逐个获取和处理健康问问模块的BUG数据")

            # 获取原始BUG数据
            raw_bugs = self._get_filtered_bugs_from_api(limit)
            logger.info(f"从TAPD获取到 {len(raw_bugs)} 个符合条件的BUG")

            processed_count = 0

            # 逐个处理BUG数据
            for i, bug_item in enumerate(raw_bugs, 1):
                try:
                    raw_bug = bug_item.get("Bug", {})
                    bug_id = raw_bug.get("id", f"unknown_{i}")

                    logger.info(f"处理第 {i}/{len(raw_bugs)} 个BUG: {bug_id}")

                    # 处理单个BUG数据
                    processed_bug = tap_client.process_bug_data(raw_bug, self.workspace_id)
                    bug_title = processed_bug.get("标题", "")
                    if "接口自动化" in bug_title:
                        logger.info("接口自动化不进行处理")
                        continue
                    if "【埋点】" in bug_title:
                        logger.info("埋点不进行处理")
                        continue
                    if "【配置自动化测试】" in bug_title:
                        logger.info("配置自动化测试不进行处理")
                        continue
                    if processed_bug:
                        processed_count += 1
                        logger.info(f"BUG {bug_id} 处理成功")
                        yield processed_bug
                    else:
                        logger.warning(f"BUG {bug_id} 处理失败，跳过")

                except Exception as e:
                    logger.error(f"处理第 {i} 个BUG失败: {str(e)}")
                    continue

            logger.info(f"BUG数据处理完成，成功处理 {processed_count} 个BUG")

        except Exception as e:
            logger.error(f"获取BUG数据失败: {str(e)}")
            return
    
    def _get_filtered_bugs_from_api(self, limit: int) -> List[Dict]:
        """
        通过API参数直接过滤BUG数据，支持分页获取

        Args:
            limit: 限制数量

        Returns:
            List[Dict]: 过滤后的BUG数据
        """
        try:
            all_bugs = []
            page = 1
            page_size = min(200, limit)  # 每页最多100个

            logger.info(f"开始分页获取BUG数据，目标数量: {limit}")

            while len(all_bugs) < limit:
                # 构建API请求参数
                params = {
                    'workspace_id': self.workspace_id,
                    'limit': page_size,
                    'page': page,
                    'order': 'created desc',
                }

                # 添加模块过滤 - 健康问问相关模块
                health_modules = ["健康问问"]
                params['module'] = "|".join(health_modules)

                logger.info(f"获取第 {page} 页数据，每页 {page_size} 个")

                # 调用TAPD API获取BUG数据
                bugs = self._call_tapd_bugs_api(params)

                if not bugs:
                    logger.info(f"第 {page} 页没有数据，停止获取")
                    break

                all_bugs.extend(bugs)

                # 如果这一页的数据少于page_size，说明已经是最后一页
                if len(bugs) < page_size:
                    logger.info("已获取到最后一页数据")
                    break

                page += 1

                # 避免无限循环
                if page > 50:  # 最多获取50页
                    logger.warning("已达到最大页数限制(50页)，停止获取")
                    break

            # 截取到指定数量
            result_bugs = all_bugs[:limit]
            # 过滤reject_time为空的BUG
            logger.info(f"分页获取完成，共获取 {len(result_bugs)} 个符合条件的BUG")
            
            filtered_bugs = [
                bug for bug in result_bugs
                if bug.get("Bug", {}).get("reject_time") in (None, "", "0000-00-00 00:00:00")
            ]
            logger.info(f"过滤后共 {len(filtered_bugs)} 个符合条件的BUG")
            return filtered_bugs

        except Exception as e:
            logger.error(f"API分页获取BUG失败: {str(e)}")
            return []


    
    def get_bug_by_id(self, bug_id: str) -> Optional[Dict]:
        """
        根据ID获取单个BUG数据

        Args:
            bug_id: BUG ID

        Returns:
            Optional[Dict]: BUG数据，如果不存在则返回None
        """
        try:
            logger.info(f"获取BUG数据: {bug_id}")

            # 获取原始BUG数据
            raw_bug = tap_client.get_bug_by_id(self.workspace_id, bug_id)
            if not raw_bug:
                logger.warning(f"未找到BUG: {bug_id}")
                return None

            # 处理BUG数据
            processed_bug = tap_client.process_bug_data(raw_bug, self.workspace_id)
            return processed_bug

        except Exception as e:
            logger.error(f"获取BUG数据失败: {str(e)}")
            return None
    
    def get_bugs_by_ids(self, bug_ids: List[str]) -> List[Dict]:
        """
        根据ID列表批量获取BUG数据
        
        Args:
            bug_ids: BUG ID列表
            
        Returns:
            List[Dict]: 符合条件的BUG数据列表
        """
        bugs = []
        for bug_id in bug_ids:
            bug_data = self.get_bug_by_id(bug_id)
            if bug_data:
                bugs.append(bug_data)
        
        logger.info(f"批量获取BUG数据完成，输入 {len(bug_ids)} 个ID，返回 {len(bugs)} 个有效BUG")
        return bugs
    
    def get_recent_bugs(self, limit: int = 100) -> List[Dict]:
        """
        获取BUG数据

        Args:
            limit: 最大数量

        Returns:
            List[Dict]: BUG数据列表
        """
        return list(self.get_bugs_for_labeling(limit=limit))
    
    def get_statistics(self) -> Dict:
        """
        获取BUG数据统计信息

        Returns:
            Dict: 统计信息
        """
        try:
            bugs = list(self.get_bugs_for_labeling(limit=1000))

            # 统计各种信息
            total_count = len(bugs)
            status_count = {}
            module_count = {}

            for bug in bugs:
                status = bug.get("状态", "未知")
                module = bug.get("模块", "未知")

                status_count[status] = status_count.get(status, 0) + 1
                module_count[module] = module_count.get(module, 0) + 1

            return {
                "总数量": total_count,
                "状态分布": status_count,
                "模块分布": module_count
            }

        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}

    def _call_tapd_bugs_api(self, params: Dict) -> List[Dict]:
        """
        调用TAPD API获取BUG数据

        Args:
            params: API请求参数

        Returns:
            List[Dict]: BUG数据列表
        """
        try:
            import requests
            response = requests.get(
                f"{tap_client.base_url}/bugs",
                headers=tap_client.headers,
                params=params,
                timeout=10
            )
            response.raise_for_status()
            data = response.json()

            if data.get('status') != 1:
                logger.error(f"API返回异常状态码: {data.get('status')}")
                return []

            return data.get('data', [])

        except Exception as e:
            logger.error(f"调用TAPD API失败: {str(e)}")
            return []


# 创建全局服务实例
bug_data_service = BugDataService()


if __name__ == "__main__":
    # 测试代码
    service = BugDataService()

    # 获取BUG数据
    bugs = service.get_recent_bugs(limit=10)
    print(f"获取到 {len(bugs)} 个BUG")

    # 打印前几个BUG的基本信息
    for i, bug in enumerate(bugs[:3]):
        print(f"\nBUG {i+1}:")
        print(f"  ID: {bug.get('ID', '')}")
        print(f"  标题: {bug.get('标题', '')}")
        print(f"  模块: {bug.get('模块', '')}")
        print(f"  状态: {bug.get('状态', '')}")

    # 获取统计信息
    stats = service.get_statistics()
    print(f"\n统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
