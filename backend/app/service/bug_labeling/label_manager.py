"""
缺陷标签管理模块
用于管理具体功能和问题类型标签的持久化存储和动态更新
"""
import json
import os
from typing import List, Dict, Set
from pathlib import Path
from backend.app.utils.logger_util import logger


class LabelManager:
    """标签管理器，负责标签的存储、更新和查询"""
    
    def __init__(self, labels_file_path: str = None):
        """
        初始化标签管理器
        
        Args:
            labels_file_path: 标签文件路径，默认为项目根目录下的labels.json
        """
        if labels_file_path is None:
            # 默认存储在项目根目录下
            project_root = Path(__file__).parent.parent.parent.parent.parent
            labels_file_path = project_root / "labels.json"
        
        self.labels_file_path = Path(labels_file_path)
        self.labels_data = self._load_labels()
    
    def _load_labels(self) -> Dict:
        """从文件加载标签数据"""
        try:
            if self.labels_file_path.exists():
                with open(self.labels_file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.info(f"成功加载标签数据，共{len(data.get('问题类型', []))}个问题类型，{len(data.get('具体功能', []))}个具体功能")
                    return data
            else:
                # 如果文件不存在，创建默认标签数据
                default_data = self._get_default_labels()
                self._save_labels(default_data)
                logger.info("创建默认标签数据文件")
                return default_data
        except Exception as e:
            logger.error(f"加载标签数据失败: {str(e)}")
            # 返回默认标签数据
            return self._get_default_labels()
    
    def _get_default_labels(self) -> Dict:
        """获取默认标签数据"""
        return {
            "问题类型": [
                "交互逻辑问题/操作冲突",
                "交互逻辑问题/交互反馈异常", 
                "功能逻辑问题/数据未显示",
                "功能逻辑问题/跳转逻辑异常",
                "界面UI问题/布局遮挡",
                "界面UI问题/交互显示异常"
            ],
            "具体功能": [
                "智能导诊/性别年龄选择",
                "智能导诊/无需细分",
                "智能导诊/胸部部位选择", 
                "智能导诊/入口跳转",
                "下滑箭头",
                "历史聊天加载",
                "页面滚动"
            ]
        }
    
    def _save_labels(self, data: Dict = None) -> bool:
        """保存标签数据到文件"""
        try:
            if data is None:
                data = self.labels_data
            
            # 确保目录存在
            self.labels_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.labels_file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"标签数据已保存到: {self.labels_file_path}")
            return True
        except Exception as e:
            logger.error(f"保存标签数据失败: {str(e)}")
            return False
    
    def get_problem_types(self) -> List[str]:
        """获取所有问题类型标签"""
        return self.labels_data.get("问题类型", [])
    
    def get_specific_functions(self) -> List[str]:
        """获取所有具体功能标签"""
        return self.labels_data.get("具体功能", [])
    
    def get_all_labels(self) -> Dict[str, List[str]]:
        """获取所有标签数据"""
        return self.labels_data.copy()
    
    def add_problem_type(self, new_type: str) -> bool:
        """
        添加新的问题类型标签
        
        Args:
            new_type: 新的问题类型标签
            
        Returns:
            bool: 是否添加成功
        """
        try:
            if new_type not in self.labels_data.get("问题类型", []):
                self.labels_data.setdefault("问题类型", []).append(new_type)
                success = self._save_labels()
                if success:
                    logger.info(f"成功添加新问题类型标签: {new_type}")
                return success
            else:
                logger.info(f"问题类型标签已存在: {new_type}")
                return True
        except Exception as e:
            logger.error(f"添加问题类型标签失败: {str(e)}")
            return False
    
    def add_specific_function(self, new_function: str) -> bool:
        """
        添加新的具体功能标签
        
        Args:
            new_function: 新的具体功能标签
            
        Returns:
            bool: 是否添加成功
        """
        try:
            if new_function not in self.labels_data.get("具体功能", []):
                self.labels_data.setdefault("具体功能", []).append(new_function)
                success = self._save_labels()
                if success:
                    logger.info(f"成功添加新具体功能标签: {new_function}")
                return success
            else:
                logger.info(f"具体功能标签已存在: {new_function}")
                return True
        except Exception as e:
            logger.error(f"添加具体功能标签失败: {str(e)}")
            return False
    
    def is_problem_type_exists(self, problem_type: str) -> bool:
        """检查问题类型标签是否存在"""
        return problem_type in self.labels_data.get("问题类型", [])
    
    def is_specific_function_exists(self, specific_function: str) -> bool:
        """检查具体功能标签是否存在"""
        return specific_function in self.labels_data.get("具体功能", [])
    
    def reload_labels(self) -> bool:
        """重新加载标签数据"""
        try:
            self.labels_data = self._load_labels()
            return True
        except Exception as e:
            logger.error(f"重新加载标签数据失败: {str(e)}")
            return False
    
    def get_labels_summary(self) -> Dict[str, int]:
        """获取标签数量统计"""
        return {
            "问题类型数量": len(self.labels_data.get("问题类型", [])),
            "具体功能数量": len(self.labels_data.get("具体功能", []))
        }


# 创建全局标签管理器实例
label_manager = LabelManager()


if __name__ == "__main__":
    # 测试代码
    manager = LabelManager()
    
    print("当前标签数据:")
    print(f"问题类型: {manager.get_problem_types()}")
    print(f"具体功能: {manager.get_specific_functions()}")
    print(f"标签统计: {manager.get_labels_summary()}")
    
    # 测试添加新标签
    manager.add_problem_type("测试问题类型")
    manager.add_specific_function("测试具体功能")
    
    print("\n添加测试标签后:")
    print(f"标签统计: {manager.get_labels_summary()}")
